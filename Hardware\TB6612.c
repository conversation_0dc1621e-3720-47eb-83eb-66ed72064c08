#include "TB6612.h"
#include <math.h>
#include <stdio.h>
#define TURNCAR 38

// PID控制器全局变量
static PID_Controller motor_pid;
static uint8_t pid_initialized = 0;

/**************************************************************************
功能：TB6612初始化函数
入口参数：定时器参数
返回  值：无
**************************************************************************/
void MOTOR_Init()
{
	TB6612_Init(100-1,168-1);  
	motor_mode();
	// 初始化PWM输出为0
	TIM_SetCompare3(TIM3,0); // Motor1	
	TIM_SetCompare4(TIM3,0); // Motor2
	TIM_SetCompare3(TIM4,0); // Motor3    
	TIM_SetCompare4(TIM8,0); // Motor4
	
	// 初始化编码器
	Encoder_Init();
}

void TB6612_Init(int arr, int psc)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	TIM_OCInitTypeDef TIM_OCInitTypeStrue;
	
	// 使能所需的GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_GPIOC | RCC_APB2Periph_GPIOD | RCC_APB2Periph_GPIOE, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3 | RCC_APB1Periph_TIM4, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE); // TIM8是高级定时器，在APB2总线上
	
	// 配置TIM3的PWM输出引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	
	// TIM3 CH3和CH4的引脚配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1; // 假设TIM3_CH3和TIM3_CH4对应的是PB0和PB1
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	// TIM4 CH3的引脚重映射配置到PD14
	GPIO_PinRemapConfig(GPIO_Remap_TIM4, ENABLE); // 启用TIM4完全重映射
	
	// TIM4 CH3的引脚配置(PD14)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// TIM8 CH4的引脚配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9; // TIM8_CH4对应的是PC9
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	// 配置TIM3
	TIM_TimeBaseInitStrue.TIM_Period = arr;
	TIM_TimeBaseInitStrue.TIM_Prescaler = psc;
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStrue);
	
	// 配置TIM4
	TIM_TimeBaseInit(TIM4, &TIM_TimeBaseInitStrue);
	
	// 配置TIM8
	TIM_TimeBaseInit(TIM8, &TIM_TimeBaseInitStrue);
	
	// 配置PWM模式
	TIM_OCInitTypeStrue.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitTypeStrue.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OCInitTypeStrue.TIM_OutputState = TIM_OutputState_Enable;
	
	// 配置TIM3 CH3和CH4
	TIM_OC3Init(TIM3, &TIM_OCInitTypeStrue);
	TIM_OC4Init(TIM3, &TIM_OCInitTypeStrue);
	
	// 配置TIM4 CH3
	TIM_OC3Init(TIM4, &TIM_OCInitTypeStrue);
	
	// 配置TIM8 CH4
	TIM_OC4Init(TIM8, &TIM_OCInitTypeStrue);
	
	// 使能预装载
	TIM_OC3PreloadConfig(TIM3, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM3, TIM_OCPreload_Enable);
	TIM_OC3PreloadConfig(TIM4, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM8, TIM_OCPreload_Enable);
	
	TIM_ARRPreloadConfig(TIM3, ENABLE);
	TIM_ARRPreloadConfig(TIM4, ENABLE);
	TIM_ARRPreloadConfig(TIM8, ENABLE);
	
	// TIM8作为高级定时器需要额外使能主输出
	TIM_CtrlPWMOutputs(TIM8, ENABLE);
	
	// 使能定时器
	TIM_Cmd(TIM3, ENABLE);
	TIM_Cmd(TIM4, ENABLE);
	TIM_Cmd(TIM8, ENABLE);
}

void motor_mode()
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 配置Motor1的方向控制引脚 - PC4和PC5
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	// 配置Motor2的方向控制引脚 - PE7和PE8
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOE, &GPIO_InitStructure);
	
	// 配置Motor3和Motor4的方向控制引脚 - PD10、PD11和PD15
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_15;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// 初始状态设置
	M1 = 1;
	M2 = 1;
	M3 = 1;
	M4 = 1;
	M5 = 1; 
	M6 = 1;
	M7 = 1;
	M8 = 1;
}

/**************************************************************************
功能：设置PWM值
入口参数：PWM值
返回  值：无
**************************************************************************/
void SetPWM(int pwm)
{
  if(pwm>=0) // 正转
  {
		M1=0; // PC4=0
		M2=1; // PC5=1
		TIM_SetCompare3(TIM3, pwm); // 使用TIM3_CH3
  }
  else if(pwm<0) // 反转
  {
		M1=1; // PC4=1
		M2=0; // PC5=0
		TIM_SetCompare3(TIM3, -pwm); // 使用TIM3_CH3
  }
}

void Set_Motor(s32 leftspeed, s32 rightspeed)
{
	// 左侧电机控制 - Motor1和Motor2
	if(leftspeed < 0)
	{
		// Motor1反转
		GPIO_ResetBits(GPIOC, GPIO_Pin_4);
		GPIO_SetBits(GPIOC, GPIO_Pin_5);
		// Motor2反转
		GPIO_ResetBits(GPIOE, GPIO_Pin_7);
		GPIO_SetBits(GPIOE, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM3, -leftspeed); // Motor1
		TIM_SetCompare4(TIM3, -leftspeed); // Motor2
	}
	else if(leftspeed >= 0)
	{
		// Motor1正转
		GPIO_SetBits(GPIOC, GPIO_Pin_4);
		GPIO_ResetBits(GPIOC, GPIO_Pin_5);
		// Motor2正转
		GPIO_SetBits(GPIOE, GPIO_Pin_7);
		GPIO_ResetBits(GPIOE, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM3, leftspeed); // Motor1
		TIM_SetCompare4(TIM3, leftspeed); // Motor2
	}
	
	// 右侧电机控制 - Motor3和Motor4
	if(rightspeed < 0)
	{
		// Motor3反转
		GPIO_ResetBits(GPIOD, GPIO_Pin_10);
		GPIO_SetBits(GPIOD, GPIO_Pin_11);
		// Motor4反转
		GPIO_ResetBits(GPIOD, GPIO_Pin_15);
		GPIO_SetBits(GPIOC, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM4, -rightspeed); // Motor3
		TIM_SetCompare4(TIM8, -rightspeed); // Motor4
	}
	else if(rightspeed >= 0)
	{
		// Motor3正转
		GPIO_SetBits(GPIOD, GPIO_Pin_10);
		GPIO_ResetBits(GPIOD, GPIO_Pin_11);
		// Motor4正转
		GPIO_SetBits(GPIOD, GPIO_Pin_15);
		GPIO_ResetBits(GPIOC, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM4, rightspeed); // Motor3
		TIM_SetCompare4(TIM8, rightspeed); // Motor4
	}
}

void Move_stop(void)
{
	// 停止所有电机
	TIM_SetCompare3(TIM3, 0); // Motor1
	TIM_SetCompare4(TIM3, 0); // Motor2
	TIM_SetCompare3(TIM4, 0); // Motor3
	TIM_SetCompare4(TIM8, 0); // Motor4
}

void Car_Retreat(void)
{
     Set_Motor(-TURNCAR, TURNCAR);
}

void Car_Letreat(void)
{
     Set_Motor(TURNCAR, -TURNCAR);
}

/**************************************************************************
功能：编码器初始化函数
入口参数：无
返回  值：无
说明：仅使用TIM2作为编码器
**************************************************************************/
void Encoder_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	
	// 使能GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
	
	// 配置TIM2编码器引脚：PA0(TIM2_CH1)、PA1(TIM2_CH2)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 编码器输入配置为浮空输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	// 配置TIM2为编码器模式
	TIM_TimeBaseInitStrue.TIM_Period = 0xFFFF; // 16位最大计数值
	TIM_TimeBaseInitStrue.TIM_Prescaler = 0;   // 不分频
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseInitStrue);
	
	// 设置TIM2为编码器接口模式
	TIM_EncoderInterfaceConfig(TIM2, TIM_EncoderMode_TI12,   TIM_ICPolarity_Rising, TIM_ICPolarity_Rising);
	
	// 清零计数器
	TIM_SetCounter(TIM2, 0);
	
	// 使能定时器
	TIM_Cmd(TIM2, ENABLE);
}

/**************************************************************************
功能：获取编码器计数值
入口参数：无
返回  值：编码器计数值（有符号，支持正负方向）
说明：TIM2编码器用于PID控制
**************************************************************************/
int32_t Get_Encoder_Count(void)
{
	// 直接返回uint16_t值，避免int16_t溢出问题
	// 对于编码器应用，通常不需要负值
	return (int32_t)TIM_GetCounter(TIM2);
}

/**************************************************************************
功能：重置编码器计数
入口参数：无
返回  值：无
说明：将TIM2计数器清零
**************************************************************************/
void Reset_Encoder(void)
{
	TIM_SetCounter(TIM2, 0);
}

// ======================= PID控制算法实现 =======================

/**************************************************************************
功能：PID控制器初始化
入口参数：PID控制器指针，Kp Ki Kd参数
返回  值：无
说明：初始化PID参数和限制值
**************************************************************************/
void PID_Init(PID_Controller* pid, float kp, float ki, float kd)
{
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    pid->target = 0;
    pid->current = 0;
    pid->error = 0;
    pid->last_error = 0;
    pid->integral = 0;
    pid->output = 0;
    pid->integral_max = PID_INTEGRAL_MAX;
    pid->output_max = PID_OUTPUT_MAX;
    pid->enable = 1;
    
    pid_initialized = 1;
}

/**************************************************************************
功能：PID控制算法计算
入口参数：PID控制器指针，当前位置值
返回  值：PID输出值
说明：增量式PID算法，带积分限幅
**************************************************************************/
float PID_Calculate(PID_Controller* pid, float current)
{
    if(!pid->enable) return 0;
    
    // 更新当前位置和误差
    pid->current = current;
    pid->error = pid->target - pid->current;
    
    // 积分项计算（带限幅）
    pid->integral += pid->error;
    if(pid->integral > pid->integral_max) 
        pid->integral = pid->integral_max;
    else if(pid->integral < -pid->integral_max) 
        pid->integral = -pid->integral_max;
    
    // 微分项计算
    float derivative = pid->error - pid->last_error;
    
    // PID输出计算
    pid->output = pid->Kp * pid->error + 
                  pid->Ki * pid->integral + 
                  pid->Kd * derivative;
    
    // 输出限幅
    if(pid->output > pid->output_max) 
        pid->output = pid->output_max;
    else if(pid->output < -pid->output_max) 
        pid->output = -pid->output_max;
    
    // 更新上次误差
    pid->last_error = pid->error;
    
    return pid->output;
}

/**************************************************************************
功能：圈数转换为编码器脉冲数
入口参数：转动圈数（正数顺时针，负数逆时针）
返回  值：脉冲数
说明：根据编码器分辨率转换
**************************************************************************/
int32_t Turns_To_Pulses(float turns)
{
    return (int32_t)(turns * ENCODER_RESOLUTION);
}

/**************************************************************************
功能：获取当前电机位置
入口参数：无
返回  值：当前位置（脉冲数）
说明：读取编码器计数值作为位置反馈
**************************************************************************/
float Get_Motor_Position(void)
{
    return (float)Get_Encoder_Count();
}

/**************************************************************************
功能：单个电机PWM控制
入口参数：电机编号，PWM值（-99到99）
返回  值：无
说明：根据电机编号设置对应的PWM和方向
**************************************************************************/
void Set_Motor_PWM(Motor_ID motor, int pwm)
{
    // PWM限制
    if(pwm > PID_OUTPUT_MAX) pwm = PID_OUTPUT_MAX;
    if(pwm < -PID_OUTPUT_MAX) pwm = -PID_OUTPUT_MAX;
    
    switch(motor)
    {
        case MOTOR1:
            if(pwm >= 0) {
                GPIO_SetBits(GPIOC, GPIO_Pin_4);     // M1=1
                GPIO_ResetBits(GPIOC, GPIO_Pin_5);   // M2=0
                TIM_SetCompare3(TIM3, pwm);
            } else {
                GPIO_ResetBits(GPIOC, GPIO_Pin_4);   // M1=0
                GPIO_SetBits(GPIOC, GPIO_Pin_5);     // M2=1
                TIM_SetCompare3(TIM3, -pwm);
            }
            break;
            
        case MOTOR2:
            if(pwm >= 0) {
                GPIO_SetBits(GPIOE, GPIO_Pin_7);     // M3=1
                GPIO_ResetBits(GPIOE, GPIO_Pin_8);   // M4=0
                TIM_SetCompare4(TIM3, pwm);
            } else {
                GPIO_ResetBits(GPIOE, GPIO_Pin_7);   // M3=0
                GPIO_SetBits(GPIOE, GPIO_Pin_8);     // M4=1
                TIM_SetCompare4(TIM3, -pwm);
            }
            break;
            
        case MOTOR3:
            if(pwm >= 0) {
                GPIO_SetBits(GPIOD, GPIO_Pin_10);    // M5=1
                GPIO_ResetBits(GPIOD, GPIO_Pin_11);  // M6=0
                TIM_SetCompare3(TIM4, pwm);
            } else {
                GPIO_ResetBits(GPIOD, GPIO_Pin_10);  // M5=0
                GPIO_SetBits(GPIOD, GPIO_Pin_11);    // M6=1
                TIM_SetCompare3(TIM4, -pwm);
            }
            break;
            
        case MOTOR4:
            if(pwm >= 0) {
                GPIO_SetBits(GPIOD, GPIO_Pin_15);    // M7=1
                GPIO_ResetBits(GPIOC, GPIO_Pin_8);   // M8=0
                TIM_SetCompare4(TIM8, pwm);
            } else {
                GPIO_ResetBits(GPIOD, GPIO_Pin_15);  // M7=0
                GPIO_SetBits(GPIOC, GPIO_Pin_8);     // M8=1
                TIM_SetCompare4(TIM8, -pwm);
            }
            break;
    }
}

/**************************************************************************
功能：电机位置PID控制主函数
入口参数：电机编号，目标圈数
返回  值：无
说明：主控制函数，使用编码器反馈进行位置环控制
**************************************************************************/
void MotorPID_GO(Motor_ID motor, float turns)
{
    // 检查PID是否已初始化，如果没有则使用默认参数初始化
    if(!pid_initialized) {
        PID_Init(&motor_pid, 1.0f, 0.01f, 0.1f); // 调整PID参数：增加Kp，添加Ki和Kd
    }

    // 重置编码器计数
    Reset_Encoder();

    // 设置目标位置
    motor_pid.target = Turns_To_Pulses(turns);
    motor_pid.integral = 0; // 清除积分累积
    motor_pid.last_error = 0;

    // 控制循环
    uint32_t timeout = 0;
    const uint32_t max_timeout = 50000; // 减少超时时间，避免过度转动
    
    while(1)
    {
        // 获取当前位置
        float current_position = Get_Motor_Position();

        // 计算PID输出
        float pid_output = PID_Calculate(&motor_pid, current_position);

        // 检查是否到达目标位置（使用绝对误差判断）
        if(fabsf(motor_pid.error) <= POSITION_TOLERANCE) {
            break; // 到达目标位置，退出控制循环
        }

        // 防止编码器计数异常导致的无限循环
        if(current_position > motor_pid.target * 2) {
            break; // 如果当前位置超过目标位置的2倍，强制退出
        }
        
        // 设置电机PWM
        if(motor == MOTOR1) {
            // 主电机：使用PID控制
            Set_Motor_PWM(MOTOR1, (int)pid_output);
            
            // 从电机：按比例跟随（可根据需要调整比例）
            Set_Motor_PWM(MOTOR2, (int)pid_output);
            Set_Motor_PWM(MOTOR3, (int)pid_output);
            Set_Motor_PWM(MOTOR4, (int)pid_output);
        } else {
            // 如果指定其他电机，仅控制该电机（需要用户手动连接编码器到对应电机）
            Set_Motor_PWM(motor, (int)pid_output);
        }
        
        // 超时保护
        timeout++;
        if(timeout > max_timeout) {
            break;
        }
        
        // 简单延时（实际使用时建议用定时器中断）
        for(volatile int i = 0; i < 1000; i++);
    }
    
    // 停止所有电机
    Set_Motor_PWM(MOTOR1, 0);
    Set_Motor_PWM(MOTOR2, 0);
    Set_Motor_PWM(MOTOR3, 0);
    Set_Motor_PWM(MOTOR4, 0);
}

/**************************************************************************
功能：电机位置PID控制主函数（带调试输出）
入口参数：电机编号，目标圈数
返回  值：无
说明：调试版本，可以通过串口输出调试信息
**************************************************************************/
void MotorPID_GO_Debug(Motor_ID motor, float turns)
{
    // 检查PID是否已初始化，如果没有则使用默认参数初始化
    if(!pid_initialized) {
        PID_Init(&motor_pid, 1.0f, 0.01f, 0.1f); // 调整PID参数
    }

    // 重置编码器计数
    Reset_Encoder();

    // 设置目标位置
    motor_pid.target = Turns_To_Pulses(turns);
    motor_pid.integral = 0; // 清除积分累积
    motor_pid.last_error = 0;

    // 输出初始调试信息
    printf("目标圈数: %.2f, 目标脉冲: %.0f\r\n", turns, motor_pid.target);

    // 控制循环
    uint32_t timeout = 0;
    const uint32_t max_timeout = 50000;
    uint32_t debug_counter = 0;

    while(1)
    {
        // 获取当前位置
        float current_position = Get_Motor_Position();

        // 计算PID输出
        float pid_output = PID_Calculate(&motor_pid, current_position);

        // 每1000次循环输出一次调试信息
        if(debug_counter % 1000 == 0) {
            printf("当前位置: %.0f, 误差: %.0f, PID输出: %.2f\r\n",
                   current_position, motor_pid.error, pid_output);
        }
        debug_counter++;

        // 检查是否到达目标位置
        if(fabsf(motor_pid.error) <= POSITION_TOLERANCE) {
            printf("到达目标位置！最终位置: %.0f\r\n", current_position);
            break;
        }

        // 防止编码器计数异常导致的无限循环
        if(current_position > motor_pid.target * 2) {
            printf("位置异常！当前位置: %.0f 超过目标的2倍\r\n", current_position);
            break;
        }

        // 设置电机PWM
        if(motor == MOTOR1) {
            Set_Motor_PWM(MOTOR1, (int)pid_output);
            Set_Motor_PWM(MOTOR2, (int)pid_output);
            Set_Motor_PWM(MOTOR3, (int)pid_output);
            Set_Motor_PWM(MOTOR4, (int)pid_output);
        } else {
            Set_Motor_PWM(motor, (int)pid_output);
        }

        // 超时保护
        timeout++;
        if(timeout > max_timeout) {
            printf("超时退出！当前位置: %.0f\r\n", current_position);
            break;
        }

        // 简单延时
        for(volatile int i = 0; i < 1000; i++);
    }

    // 停止所有电机
    Set_Motor_PWM(MOTOR1, 0);
    Set_Motor_PWM(MOTOR2, 0);
    Set_Motor_PWM(MOTOR3, 0);
    Set_Motor_PWM(MOTOR4, 0);
}
