#ifndef __TB6612_H 
#define __TB6612_H 
#include "sys.h"
#include "TIM8.h"
#include "TIM3.h"
#include "TIM4.h"

// PID控制相关定义
#define ENCODER_PPR 50          // 编码器每转脉冲数
#define ENCODER_RESOLUTION (ENCODER_PPR * 4)  // 四倍频后的分辨率 = 200脉冲/圈
#define PID_INTEGRAL_MAX 1000    // 积分限幅
#define PID_OUTPUT_MAX 10        // PID输出限幅（PWM最大值）
#define POSITION_TOLERANCE 5     // 位置到达容差（脉冲数）

// 电机标识枚举
typedef enum {
    MOTOR1 = 1,
    MOTOR2 = 2, 
    MOTOR3 = 3,
    MOTOR4 = 4
} Motor_ID;

// PID控制器结构体
typedef struct {
    float Kp, Ki, Kd;           // PID参数
    float target;               // 目标位置（脉冲数）
    float current;              // 当前位置
    float error;                // 当前误差
    float last_error;           // 上次误差
    float integral;             // 积分累积
    float output;               // PID输出
    float integral_max;         // 积分限幅
    float output_max;           // 输出限幅
    uint8_t enable;             // PID使能标志
} PID_Controller;

// 根据电路图修改引脚定义
// U3 TB6612
#define M1 PCout(5)   // AIN1
#define M2 PCout(4)   // AIN2
#define M3 PEout(7)   // BIN1
#define M4 PEout(8)   // BIN2

// U11 TB6612
#define M5 PDout(11)  // AIN1
#define M6 PDout(10)  // AIN2
#define M7 PDout(15)  // BIN1
#define M8 PCout(8)   // BIN2

void MOTOR_Init(void);

void TB6612_Init(int arr, int psc);
void SetPWM(int pwm);
void motor_mode(void);
void Set_Motor(s32 leftspeed,s32 rightspeed);
void Move_stop(void);
void Car_Retreat(void);
void Car_Letreat(void);

// 编码器相关函数声明
void Encoder_Init(void);           // 编码器初始化（仅TIM2）
int32_t Get_Encoder_Count(void);   // 获取编码器计数
void Reset_Encoder(void);          // 重置编码器计数

// PID控制相关函数声明
void PID_Init(PID_Controller* pid, float kp, float ki, float kd);
float PID_Calculate(PID_Controller* pid, float current);
void MotorPID_GO(Motor_ID motor, float turns);
void MotorPID_GO_Debug(Motor_ID motor, float turns); // 带调试输出的版本
void Set_Motor_PWM(Motor_ID motor, int pwm);
float Get_Motor_Position(void);
int32_t Turns_To_Pulses(float turns);

#endif 
