#include "stm32f10x.h"
#include "sys.h"
#include "delay.h"
#include "usart1.h"
#include "oled.h"
#include "grayscale.h"
#include "gray_go.h"
#include "PCA9685.h"
#include "TCS34725.h"
#include "Task_Working.h"
#include "TB6612.h"

void init (){
    delay_init(8);
    Usart1_Init(9600);
    OLED_Init();
    Grayscale_Init();
    PCA9685_Init();
    TB6612_Init(65534,0);
    Encoder_Init();
    TCS34725_Init();
    MOTOR_Init();
}

void main (){
	

	init();
    MotorPID_GO(MOTOR1, 1.0);
 //   MotorPID_GO(MOTOR2, 0.1);
 //   MotorPID_GO(MOTOR3, 0.1);
//    MotorPID_GO(MOTOR4, 0.1);
   
	while (1)
	{

        OLED_ShowString(4,1,"Test End");
	}
}