#include "stm32f10x.h"
#include "sys.h"
#include "delay.h"
#include "usart1.h"
#include "oled.h"
#include "grayscale.h"
#include "gray_go.h"
#include "PCA9685.h"
#include "TCS34725.h"
#include "Task_Working.h"
#include "TB6612.h"

void init (){
    delay_init(8);
    Usart1_Init(9600);
    OLED_Init();
    Grayscale_Init();
    PCA9685_Init();
    TB6612_Init(65534,0);
    Encoder_Init();
    TCS34725_Init();
    MOTOR_Init();
}

void main (){


	init();

    // 使用调试版本进行测试
    printf("开始电机PID控制测试...\r\n");
    MotorPID_GO_Debug(MOTOR1, 30.0);  // 测试转1圈

    // 等待一段时间后再次测试
    delay_ms(2000);
    printf("第二次测试...\r\n");
    MotorPID_GO_Debug(MOTOR1, 0.5);  // 测试转0.5圈

	while (1)
	{
        OLED_ShowString(4,1,"Test End");
        delay_ms(1000);
	}
}