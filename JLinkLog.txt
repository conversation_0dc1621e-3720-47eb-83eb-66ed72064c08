T58F4 000:005.911   SEGGER J-Link V6.84a Log File
T58F4 000:006.044   DLL Compiled: Sep  7 2020 17:26:08
T58F4 000:006.048   Logging started @ 2025-06-23 15:19
T58F4 000:006.052 - 6.054ms
T58F4 000:006.061 JLINK_SetWarnOutHandler(...)
T58F4 000:006.086 - 0.026ms
T58F4 000:006.091 JLINK_OpenEx(...)
T58F4 000:008.812   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T58F4 000:010.273   Hardware: V7.00
T58F4 000:010.298   S/N: 20090928
T58F4 000:010.304   OEM: SEGGER
T58F4 000:010.310   Feature(s): jflash
T58F4 000:010.994   TELNET listener socket opened on port 19021
T58F4 000:011.090   WEBSRV Starting webserver
T58F4 000:011.244   WEBSRV Webserver running on local port 19080
T58F4 000:011.252 - 5.163ms returns "O.K."
T58F4 000:011.270 JLINK_GetEmuCaps()
T58F4 000:011.274 - 0.006ms returns 0x88EA5833
T58F4 000:011.281 JLINK_TIF_GetAvailable(...)
T58F4 000:011.410 - 0.132ms
T58F4 000:011.419 JLINK_SetErrorOutHandler(...)
T58F4 000:011.423 - 0.005ms
T58F4 000:011.433 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\project\GCC\project_f103ve\JLinkSettings.ini"", ...). 
T58F4 000:016.960   Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref
T58F4 000:017.109   XML referenced by ref file: C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml
T58F4 000:017.610   C:\Program Files (x86)\SEGGER\JLink\JLinkDevices.xml evaluated successfully.
T58F4 000:035.386 - 23.967ms returns 0x00
T58F4 000:035.712 JLINK_ExecCommand("Device = STM32F103VE", ...). 
T58F4 000:036.003   Device "STM32F103VE" selected.
T58F4 000:036.347 - 0.628ms returns 0x00
T58F4 000:036.360 JLINK_GetHardwareVersion()
T58F4 000:036.364 - 0.005ms returns 70000
T58F4 000:036.372 JLINK_GetDLLVersion()
T58F4 000:036.376 - 0.005ms returns 68401
T58F4 000:036.380 JLINK_GetOEMString(...)
T58F4 000:036.385 JLINK_GetFirmwareString(...)
T58F4 000:036.388 - 0.005ms
T58F4 000:037.001 JLINK_GetDLLVersion()
T58F4 000:037.013 - 0.014ms returns 68401
T58F4 000:037.018 JLINK_GetCompileDateTime()
T58F4 000:037.022 - 0.005ms
T58F4 000:037.155 JLINK_GetFirmwareString(...)
T58F4 000:037.161 - 0.008ms
T58F4 000:037.280 JLINK_GetHardwareVersion()
T58F4 000:037.285 - 0.006ms returns 70000
T58F4 000:037.404 JLINK_GetSN()
T58F4 000:037.409 - 0.006ms returns 20090928
T58F4 000:037.525 JLINK_GetOEMString(...)
T58F4 000:037.751 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T58F4 000:038.066 - 0.322ms returns 0x00
T58F4 000:038.079 JLINK_HasError()
T58F4 000:038.087 JLINK_SetSpeed(5000)
T58F4 000:038.134 - 0.048ms
T58F4 000:038.139 JLINK_GetId()
T58F4 000:039.040   Found SW-DP with ID 0x1BA01477
T58F4 000:049.766   Found SW-DP with ID 0x1BA01477
T58F4 000:051.962   Old FW that does not support reading DPIDR via DAP jobs
T58F4 000:055.112   Unknown DP version. Assuming DPv0
T58F4 000:055.255   Scanning AP map to find all available APs
T58F4 000:057.126   AP[1]: Stopped AP scan as end of AP map has been reached
T58F4 000:057.257   AP[0]: AHB-AP (IDR: 0x14770011)
T58F4 000:057.373   Iterating through AP map to find AHB-AP to use
T58F4 000:060.747   AP[0]: Core found
T58F4 000:060.880   AP[0]: AHB-AP ROM base: 0xE00FF000
T58F4 000:062.544   CPUID register: 0x411FC231. Implementer code: 0x41 (ARM)
T58F4 000:062.668   Found Cortex-M3 r1p1, Little endian.
T58F4 000:163.692   -- Max. mem block: 0x00002C18
T58F4 000:163.733   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T58F4 000:164.291   CPU_ReadMem(4 bytes @ 0xE0002000)
T58F4 000:165.015   FPUnit: 6 code (BP) slots and 2 literal slots
T58F4 000:165.031   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T58F4 000:165.402   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T58F4 000:165.796   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:166.174   CPU_WriteMem(4 bytes @ 0xE0001000)
T58F4 000:166.555   CPU_ReadMem(4 bytes @ 0xE000ED88)
T58F4 000:166.904   CPU_WriteMem(4 bytes @ 0xE000ED88)
T58F4 000:167.297   CPU_ReadMem(4 bytes @ 0xE000ED88)
T58F4 000:167.647   CPU_WriteMem(4 bytes @ 0xE000ED88)
T58F4 000:168.411   CoreSight components:
T58F4 000:168.588   ROMTbl[0] @ E00FF000
T58F4 000:168.598   CPU_ReadMem(64 bytes @ 0xE00FF000)
T58F4 000:169.610   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T58F4 000:170.379   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 001BB000 SCS
T58F4 000:170.388   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T58F4 000:171.173   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 001BB002 DWT
T58F4 000:171.186   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T58F4 000:171.990   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 000BB003 FPB
T58F4 000:172.005   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T58F4 000:172.805   ROMTbl[0][3]: ********, CID: B105E00D, PID: 001BB001 ITM
T58F4 000:172.819   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T58F4 000:173.584   ROMTbl[0][4]: ********, CID: B105900D, PID: 001BB923 TPIU-Lite
T58F4 000:173.593   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T58F4 000:175.038   ROMTbl[0][5]: ********, CID: B105900D, PID: 101BB924 ETM-M3
T58F4 000:175.424 - 137.293ms returns 0x1BA01477
T58F4 000:175.446 JLINK_GetDLLVersion()
T58F4 000:175.450 - 0.005ms returns 68401
T58F4 000:175.456 JLINK_CORE_GetFound()
T58F4 000:175.460 - 0.005ms returns 0x30000FF
T58F4 000:175.469 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T58F4 000:175.474   Value=0xE00FF000
T58F4 000:175.480 - 0.012ms returns 0
T58F4 000:175.656 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T58F4 000:175.663   Value=0xE00FF000
T58F4 000:175.668 - 0.014ms returns 0
T58F4 000:175.673 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T58F4 000:175.677   Value=0x********
T58F4 000:175.682 - 0.010ms returns 0
T58F4 000:175.687 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T58F4 000:175.706   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T58F4 000:176.349   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T58F4 000:176.362 - 0.676ms returns 32 (0x20)
T58F4 000:176.368 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T58F4 000:176.373   Value=0x00000000
T58F4 000:176.378 - 0.011ms returns 0
T58F4 000:176.382 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T58F4 000:176.386   Value=0x********
T58F4 000:176.391 - 0.010ms returns 0
T58F4 000:176.395 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T58F4 000:176.399   Value=0x********
T58F4 000:176.404 - 0.010ms returns 0
T58F4 000:176.408 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T58F4 000:176.412   Value=0xE0001000
T58F4 000:176.417 - 0.010ms returns 0
T58F4 000:176.421 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T58F4 000:176.424   Value=0xE0002000
T58F4 000:176.429 - 0.010ms returns 0
T58F4 000:176.434 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T58F4 000:176.437   Value=0xE000E000
T58F4 000:176.442 - 0.010ms returns 0
T58F4 000:176.447 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T58F4 000:176.451   Value=0xE000EDF0
T58F4 000:176.456 - 0.010ms returns 0
T58F4 000:176.460 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T58F4 000:176.467   CPU_ReadMem(4 bytes @ 0xE000ED00)
T58F4 000:176.844   Data:  31 C2 1F 41
T58F4 000:176.854   Debug reg: CPUID
T58F4 000:176.859 - 0.400ms returns 1 (0x1)
T58F4 000:176.865 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T58F4 000:176.869   Value=0x00000000
T58F4 000:176.874 - 0.011ms returns 0
T58F4 000:176.879 JLINK_HasError()
T58F4 000:176.884 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T58F4 000:176.887 - 0.005ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T58F4 000:176.892 JLINK_Reset()
T58F4 000:176.905   CPU is running
T58F4 000:176.911   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T58F4 000:177.296   CPU is running
T58F4 000:177.305   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T58F4 000:177.892   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T58F4 000:178.448   Reset: Reset device via AIRCR.SYSRESETREQ.
T58F4 000:178.463   CPU is running
T58F4 000:178.471   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T58F4 000:230.724   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T58F4 000:231.114   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T58F4 000:231.460   CPU is running
T58F4 000:231.467   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T58F4 000:231.862   CPU is running
T58F4 000:231.868   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T58F4 000:237.292   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T58F4 000:240.508   CPU_WriteMem(4 bytes @ 0xE0002000)
T58F4 000:240.931   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T58F4 000:241.312   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:241.723 - 64.839ms
T58F4 000:241.755 JLINK_Halt()
T58F4 000:241.760 - 0.008ms returns 0x00
T58F4 000:241.767 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T58F4 000:241.780   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T58F4 000:242.154   Data:  03 00 03 00
T58F4 000:242.163   Debug reg: DHCSR
T58F4 000:242.168 - 0.403ms returns 1 (0x1)
T58F4 000:242.215 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T58F4 000:242.219   Debug reg: DHCSR
T58F4 000:242.234   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T58F4 000:242.614 - 0.401ms returns 0 (0x00000000)
T58F4 000:242.619 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T58F4 000:242.623   Debug reg: DEMCR
T58F4 000:242.630   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T58F4 000:242.989 - 0.372ms returns 0 (0x00000000)
T58F4 000:243.579 JLINK_GetHWStatus(...)
T58F4 000:243.682 - 0.104ms returns 0
T58F4 000:244.034 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T58F4 000:244.039 - 0.007ms returns 0x06
T58F4 000:244.044 JLINK_GetNumBPUnits(Type = 0xF0)
T58F4 000:244.047 - 0.005ms returns 0x2000
T58F4 000:244.052 JLINK_GetNumWPUnits()
T58F4 000:244.055 - 0.005ms returns 4
T58F4 000:244.392 JLINK_GetSpeed()
T58F4 000:244.397 - 0.006ms returns 4000
T58F4 000:244.620 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T58F4 000:244.628   CPU_ReadMem(4 bytes @ 0xE000E004)
T58F4 000:244.976   Data:  01 00 00 00
T58F4 000:244.982 - 0.363ms returns 1 (0x1)
T58F4 000:244.987 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T58F4 000:244.991   CPU_ReadMem(4 bytes @ 0xE000E004)
T58F4 000:245.328   Data:  01 00 00 00
T58F4 000:245.334 - 0.348ms returns 1 (0x1)
T58F4 000:245.339 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T58F4 000:245.343   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T58F4 000:245.517   CPU_WriteMem(28 bytes @ 0xE0001000)
T58F4 000:246.153 - 0.816ms returns 0x1C
T58F4 000:246.161 JLINK_Halt()
T58F4 000:246.165 - 0.005ms returns 0x00
T58F4 000:246.169 JLINK_IsHalted()
T58F4 000:246.173 - 0.005ms returns TRUE
T58F4 000:247.210 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T58F4 000:247.221   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T58F4 000:247.415   CPU_WriteMem(356 bytes @ 0x20000000)
T58F4 000:251.583 - 4.382ms returns 0x164
T58F4 000:251.627 JLINK_HasError()
T58F4 000:251.636 JLINK_WriteReg(R0, 0x08000000)
T58F4 000:251.649 - 0.015ms returns 0
T58F4 000:251.656 JLINK_WriteReg(R1, 0x00B71B00)
T58F4 000:251.661 - 0.007ms returns 0
T58F4 000:251.666 JLINK_WriteReg(R2, 0x00000001)
T58F4 000:251.672 - 0.007ms returns 0
T58F4 000:251.677 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:251.682 - 0.007ms returns 0
T58F4 000:251.688 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:251.693 - 0.007ms returns 0
T58F4 000:251.699 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:251.704 - 0.007ms returns 0
T58F4 000:251.709 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:251.714 - 0.007ms returns 0
T58F4 000:251.720 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:251.725 - 0.007ms returns 0
T58F4 000:251.731 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:251.739 - 0.011ms returns 0
T58F4 000:251.747 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:251.755 - 0.013ms returns 0
T58F4 000:251.765 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:251.770 - 0.008ms returns 0
T58F4 000:251.777 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:251.782 - 0.008ms returns 0
T58F4 000:251.789 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:251.794 - 0.008ms returns 0
T58F4 000:251.801 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:251.807 - 0.008ms returns 0
T58F4 000:251.813 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:251.819 - 0.008ms returns 0
T58F4 000:251.825 JLINK_WriteReg(R15 (PC), 0x20000038)
T58F4 000:251.831 - 0.008ms returns 0
T58F4 000:251.837 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:251.850 - 0.015ms returns 0
T58F4 000:251.857 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:251.862 - 0.008ms returns 0
T58F4 000:251.885 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:251.891 - 0.008ms returns 0
T58F4 000:251.897 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:251.903 - 0.008ms returns 0
T58F4 000:251.909 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:251.919   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:252.299 - 0.393ms returns 0x00000001
T58F4 000:252.307 JLINK_Go()
T58F4 000:252.315   CPU_WriteMem(2 bytes @ 0x20000000)
T58F4 000:252.710   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:253.117   CPU_WriteMem(4 bytes @ 0xE0002008)
T58F4 000:253.128   CPU_WriteMem(4 bytes @ 0xE000200C)
T58F4 000:253.133   CPU_WriteMem(4 bytes @ 0xE0002010)
T58F4 000:253.139   CPU_WriteMem(4 bytes @ 0xE0002014)
T58F4 000:253.144   CPU_WriteMem(4 bytes @ 0xE0002018)
T58F4 000:253.149   CPU_WriteMem(4 bytes @ 0xE000201C)
T58F4 000:255.231   CPU_WriteMem(4 bytes @ 0xE0001004)
T58F4 000:258.195 - 5.890ms
T58F4 000:258.203 JLINK_IsHalted()
T58F4 000:261.037   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:261.413 - 3.216ms returns TRUE
T58F4 000:261.426 JLINK_ReadReg(R15 (PC))
T58F4 000:261.434 - 0.010ms returns 0x20000000
T58F4 000:261.440 JLINK_ClrBPEx(BPHandle = 0x00000001)
T58F4 000:261.445 - 0.007ms returns 0x00
T58F4 000:261.451 JLINK_ReadReg(R0)
T58F4 000:261.456 - 0.007ms returns 0x00000000
T58F4 000:261.475 JLINK_HasError()
T58F4 000:261.482 JLINK_WriteReg(R0, 0x08000000)
T58F4 000:261.487 - 0.007ms returns 0
T58F4 000:261.493 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:261.498 - 0.007ms returns 0
T58F4 000:261.503 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:261.508 - 0.007ms returns 0
T58F4 000:261.514 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:261.519 - 0.007ms returns 0
T58F4 000:261.524 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:261.529 - 0.007ms returns 0
T58F4 000:261.535 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:261.540 - 0.007ms returns 0
T58F4 000:261.545 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:261.550 - 0.007ms returns 0
T58F4 000:261.556 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:261.561 - 0.007ms returns 0
T58F4 000:261.566 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:261.571 - 0.007ms returns 0
T58F4 000:261.577 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:261.582 - 0.007ms returns 0
T58F4 000:261.587 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:261.592 - 0.007ms returns 0
T58F4 000:261.598 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:261.603 - 0.007ms returns 0
T58F4 000:261.608 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:261.613 - 0.007ms returns 0
T58F4 000:261.619 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:261.624 - 0.007ms returns 0
T58F4 000:261.630 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:261.635 - 0.007ms returns 0
T58F4 000:261.641 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:261.645 - 0.007ms returns 0
T58F4 000:261.651 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:261.656 - 0.007ms returns 0
T58F4 000:261.662 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:261.667 - 0.007ms returns 0
T58F4 000:261.672 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:261.677 - 0.007ms returns 0
T58F4 000:261.683 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:261.688 - 0.007ms returns 0
T58F4 000:261.693 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:261.699 - 0.007ms returns 0x00000002
T58F4 000:261.705 JLINK_Go()
T58F4 000:261.714   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:264.726 - 3.030ms
T58F4 000:264.742 JLINK_IsHalted()
T58F4 000:267.572   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:267.958 - 3.220ms returns TRUE
T58F4 000:267.973 JLINK_ReadReg(R15 (PC))
T58F4 000:267.980 - 0.009ms returns 0x20000000
T58F4 000:267.988 JLINK_ClrBPEx(BPHandle = 0x00000002)
T58F4 000:267.993 - 0.007ms returns 0x00
T58F4 000:267.999 JLINK_ReadReg(R0)
T58F4 000:268.004 - 0.007ms returns 0x00000001
T58F4 000:268.012 JLINK_HasError()
T58F4 000:268.018 JLINK_WriteReg(R0, 0x08000000)
T58F4 000:268.023 - 0.007ms returns 0
T58F4 000:268.030 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:268.076 - 0.048ms returns 0
T58F4 000:268.084 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:268.089 - 0.007ms returns 0
T58F4 000:268.097 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:268.102 - 0.007ms returns 0
T58F4 000:268.109 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:268.114 - 0.007ms returns 0
T58F4 000:268.121 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:268.126 - 0.007ms returns 0
T58F4 000:268.132 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:268.137 - 0.007ms returns 0
T58F4 000:268.145 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:268.150 - 0.007ms returns 0
T58F4 000:268.157 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:268.162 - 0.007ms returns 0
T58F4 000:268.170 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:268.175 - 0.007ms returns 0
T58F4 000:268.181 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:268.186 - 0.007ms returns 0
T58F4 000:268.193 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:268.198 - 0.007ms returns 0
T58F4 000:268.206 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:268.211 - 0.007ms returns 0
T58F4 000:268.216 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:268.222 - 0.007ms returns 0
T58F4 000:268.229 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:268.234 - 0.007ms returns 0
T58F4 000:268.240 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:268.245 - 0.007ms returns 0
T58F4 000:268.253 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:268.258 - 0.007ms returns 0
T58F4 000:268.265 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:268.270 - 0.007ms returns 0
T58F4 000:268.276 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:268.281 - 0.007ms returns 0
T58F4 000:268.289 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:268.294 - 0.007ms returns 0
T58F4 000:268.301 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:268.307 - 0.007ms returns 0x00000003
T58F4 000:268.315 JLINK_Go()
T58F4 000:268.323   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:271.329 - 3.023ms
T58F4 000:271.350 JLINK_IsHalted()
T58F4 000:271.833 - 0.486ms returns FALSE
T58F4 000:271.844 JLINK_HasError()
T58F4 000:279.172 JLINK_IsHalted()
T58F4 000:279.596 - 0.432ms returns FALSE
T58F4 000:279.611 JLINK_HasError()
T58F4 000:281.169 JLINK_IsHalted()
T58F4 000:281.575 - 0.414ms returns FALSE
T58F4 000:281.588 JLINK_HasError()
T58F4 000:284.177 JLINK_IsHalted()
T58F4 000:284.573 - 0.404ms returns FALSE
T58F4 000:284.588 JLINK_HasError()
T58F4 000:286.173 JLINK_IsHalted()
T58F4 000:286.568 - 0.398ms returns FALSE
T58F4 000:286.576 JLINK_HasError()
T58F4 000:288.174 JLINK_IsHalted()
T58F4 000:288.568 - 0.400ms returns FALSE
T58F4 000:288.578 JLINK_HasError()
T58F4 000:290.581 JLINK_IsHalted()
T58F4 000:290.953 - 0.375ms returns FALSE
T58F4 000:290.962 JLINK_HasError()
T58F4 000:292.068 JLINK_IsHalted()
T58F4 000:292.407 - 0.345ms returns FALSE
T58F4 000:292.419 JLINK_HasError()
T58F4 000:294.073 JLINK_IsHalted()
T58F4 000:296.930   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:297.310 - 3.243ms returns TRUE
T58F4 000:297.328 JLINK_ReadReg(R15 (PC))
T58F4 000:297.335 - 0.008ms returns 0x20000000
T58F4 000:297.341 JLINK_ClrBPEx(BPHandle = 0x00000003)
T58F4 000:297.345 - 0.005ms returns 0x00
T58F4 000:297.350 JLINK_ReadReg(R0)
T58F4 000:297.354 - 0.005ms returns 0x00000000
T58F4 000:297.388 JLINK_HasError()
T58F4 000:297.393 JLINK_WriteReg(R0, 0x08000400)
T58F4 000:297.397 - 0.006ms returns 0
T58F4 000:297.401 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:297.405 - 0.005ms returns 0
T58F4 000:297.409 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:297.413 - 0.005ms returns 0
T58F4 000:297.417 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:297.421 - 0.005ms returns 0
T58F4 000:297.425 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:297.429 - 0.005ms returns 0
T58F4 000:297.433 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:297.437 - 0.005ms returns 0
T58F4 000:297.441 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:297.445 - 0.005ms returns 0
T58F4 000:297.449 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:297.453 - 0.005ms returns 0
T58F4 000:297.457 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:297.461 - 0.005ms returns 0
T58F4 000:297.469 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:297.475 - 0.007ms returns 0
T58F4 000:297.479 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:297.483 - 0.005ms returns 0
T58F4 000:297.487 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:297.490 - 0.005ms returns 0
T58F4 000:297.495 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:297.498 - 0.005ms returns 0
T58F4 000:297.502 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:297.506 - 0.005ms returns 0
T58F4 000:297.510 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:297.514 - 0.005ms returns 0
T58F4 000:297.518 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:297.522 - 0.005ms returns 0
T58F4 000:297.526 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:297.530 - 0.005ms returns 0
T58F4 000:297.534 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:297.538 - 0.005ms returns 0
T58F4 000:297.542 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:297.546 - 0.005ms returns 0
T58F4 000:297.550 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:297.554 - 0.005ms returns 0
T58F4 000:297.558 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:297.563 - 0.006ms returns 0x00000004
T58F4 000:297.567 JLINK_Go()
T58F4 000:297.575   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:300.597 - 8.960ms
T58F4 000:306.543 JLINK_IsHalted()
T58F4 000:309.334   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:309.701 - 3.161ms returns TRUE
T58F4 000:309.710 JLINK_ReadReg(R15 (PC))
T58F4 000:309.718 - 0.010ms returns 0x20000000
T58F4 000:309.726 JLINK_ClrBPEx(BPHandle = 0x00000004)
T58F4 000:309.731 - 0.008ms returns 0x00
T58F4 000:309.737 JLINK_ReadReg(R0)
T58F4 000:309.743 - 0.007ms returns 0x00000000
T58F4 000:309.786 JLINK_HasError()
T58F4 000:309.792 JLINK_WriteReg(R0, 0x08000800)
T58F4 000:309.798 - 0.008ms returns 0
T58F4 000:309.805 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:309.810 - 0.007ms returns 0
T58F4 000:309.816 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:309.821 - 0.007ms returns 0
T58F4 000:309.828 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:309.833 - 0.007ms returns 0
T58F4 000:309.839 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:309.844 - 0.007ms returns 0
T58F4 000:309.850 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:309.856 - 0.008ms returns 0
T58F4 000:309.862 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:309.867 - 0.007ms returns 0
T58F4 000:309.872 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:309.877 - 0.007ms returns 0
T58F4 000:309.883 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:309.888 - 0.007ms returns 0
T58F4 000:309.894 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:309.898 - 0.007ms returns 0
T58F4 000:309.904 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:309.909 - 0.007ms returns 0
T58F4 000:309.915 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:309.920 - 0.007ms returns 0
T58F4 000:309.925 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:309.930 - 0.007ms returns 0
T58F4 000:309.936 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:309.941 - 0.007ms returns 0
T58F4 000:309.947 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:309.952 - 0.007ms returns 0
T58F4 000:309.957 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:309.962 - 0.007ms returns 0
T58F4 000:309.968 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:309.973 - 0.007ms returns 0
T58F4 000:309.979 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:309.984 - 0.007ms returns 0
T58F4 000:309.989 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:309.994 - 0.007ms returns 0
T58F4 000:310.000 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:310.005 - 0.007ms returns 0
T58F4 000:310.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:310.016 - 0.008ms returns 0x00000005
T58F4 000:310.022 JLINK_Go()
T58F4 000:310.032   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:313.031 - 3.014ms
T58F4 000:313.041 JLINK_IsHalted()
T58F4 000:315.869   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:316.248 - 3.214ms returns TRUE
T58F4 000:316.262 JLINK_ReadReg(R15 (PC))
T58F4 000:316.270 - 0.010ms returns 0x20000000
T58F4 000:316.276 JLINK_ClrBPEx(BPHandle = 0x00000005)
T58F4 000:316.281 - 0.007ms returns 0x00
T58F4 000:316.287 JLINK_ReadReg(R0)
T58F4 000:316.292 - 0.011ms returns 0x00000001
T58F4 000:316.304 JLINK_HasError()
T58F4 000:316.311 JLINK_WriteReg(R0, 0x08000800)
T58F4 000:316.316 - 0.007ms returns 0
T58F4 000:316.322 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:316.327 - 0.007ms returns 0
T58F4 000:316.332 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:316.337 - 0.007ms returns 0
T58F4 000:316.343 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:316.348 - 0.007ms returns 0
T58F4 000:316.353 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:316.358 - 0.007ms returns 0
T58F4 000:316.364 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:316.369 - 0.007ms returns 0
T58F4 000:316.374 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:316.379 - 0.007ms returns 0
T58F4 000:316.385 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:316.390 - 0.007ms returns 0
T58F4 000:316.396 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:316.400 - 0.007ms returns 0
T58F4 000:316.406 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:316.411 - 0.007ms returns 0
T58F4 000:316.417 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:316.422 - 0.007ms returns 0
T58F4 000:316.427 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:316.432 - 0.007ms returns 0
T58F4 000:316.438 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:316.443 - 0.007ms returns 0
T58F4 000:316.448 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:316.453 - 0.007ms returns 0
T58F4 000:316.459 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:316.464 - 0.007ms returns 0
T58F4 000:316.470 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:316.475 - 0.007ms returns 0
T58F4 000:316.480 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:316.485 - 0.007ms returns 0
T58F4 000:316.491 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:316.496 - 0.007ms returns 0
T58F4 000:316.502 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:316.506 - 0.007ms returns 0
T58F4 000:316.512 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:316.517 - 0.007ms returns 0
T58F4 000:316.523 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:316.528 - 0.007ms returns 0x00000006
T58F4 000:316.534 JLINK_Go()
T58F4 000:316.543   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:319.573 - 3.047ms
T58F4 000:319.588 JLINK_IsHalted()
T58F4 000:319.930 - 0.345ms returns FALSE
T58F4 000:319.937 JLINK_HasError()
T58F4 000:321.241 JLINK_IsHalted()
T58F4 000:321.578 - 0.339ms returns FALSE
T58F4 000:321.585 JLINK_HasError()
T58F4 000:323.241 JLINK_IsHalted()
T58F4 000:323.577 - 0.338ms returns FALSE
T58F4 000:323.584 JLINK_HasError()
T58F4 000:325.280 JLINK_IsHalted()
T58F4 000:325.724 - 0.448ms returns FALSE
T58F4 000:325.733 JLINK_HasError()
T58F4 000:327.241 JLINK_IsHalted()
T58F4 000:327.588 - 0.353ms returns FALSE
T58F4 000:327.600 JLINK_HasError()
T58F4 000:329.241 JLINK_IsHalted()
T58F4 000:329.578 - 0.339ms returns FALSE
T58F4 000:329.585 JLINK_HasError()
T58F4 000:331.293 JLINK_IsHalted()
T58F4 000:331.670 - 0.392ms returns FALSE
T58F4 000:331.693 JLINK_HasError()
T58F4 000:333.246 JLINK_IsHalted()
T58F4 000:333.595 - 0.351ms returns FALSE
T58F4 000:333.601 JLINK_HasError()
T58F4 000:335.245 JLINK_IsHalted()
T58F4 000:335.588 - 0.346ms returns FALSE
T58F4 000:335.596 JLINK_HasError()
T58F4 000:337.596 JLINK_IsHalted()
T58F4 000:337.946 - 0.357ms returns FALSE
T58F4 000:337.959 JLINK_HasError()
T58F4 000:339.673 JLINK_IsHalted()
T58F4 000:340.063 - 0.393ms returns FALSE
T58F4 000:340.070 JLINK_HasError()
T58F4 000:341.674 JLINK_IsHalted()
T58F4 000:344.534   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:344.932 - 3.265ms returns TRUE
T58F4 000:344.946 JLINK_ReadReg(R15 (PC))
T58F4 000:344.954 - 0.010ms returns 0x20000000
T58F4 000:344.959 JLINK_ClrBPEx(BPHandle = 0x00000006)
T58F4 000:344.963 - 0.005ms returns 0x00
T58F4 000:344.968 JLINK_ReadReg(R0)
T58F4 000:344.972 - 0.005ms returns 0x00000000
T58F4 000:344.999 JLINK_HasError()
T58F4 000:345.003 JLINK_WriteReg(R0, 0x08000C00)
T58F4 000:345.008 - 0.006ms returns 0
T58F4 000:345.012 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:345.016 - 0.005ms returns 0
T58F4 000:345.020 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:345.024 - 0.005ms returns 0
T58F4 000:345.028 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:345.037 - 0.011ms returns 0
T58F4 000:345.042 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:345.045 - 0.005ms returns 0
T58F4 000:345.049 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:345.053 - 0.005ms returns 0
T58F4 000:345.057 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:345.061 - 0.005ms returns 0
T58F4 000:345.065 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:345.068 - 0.005ms returns 0
T58F4 000:345.072 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:345.076 - 0.005ms returns 0
T58F4 000:345.080 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:345.084 - 0.005ms returns 0
T58F4 000:345.088 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:345.092 - 0.005ms returns 0
T58F4 000:345.096 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:345.099 - 0.005ms returns 0
T58F4 000:345.104 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:345.107 - 0.005ms returns 0
T58F4 000:345.111 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:345.115 - 0.005ms returns 0
T58F4 000:345.120 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:345.123 - 0.005ms returns 0
T58F4 000:345.127 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:345.131 - 0.005ms returns 0
T58F4 000:345.135 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:345.139 - 0.005ms returns 0
T58F4 000:345.143 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:345.147 - 0.005ms returns 0
T58F4 000:345.151 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:345.155 - 0.005ms returns 0
T58F4 000:345.159 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:345.162 - 0.005ms returns 0
T58F4 000:345.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:345.171 - 0.005ms returns 0x00000007
T58F4 000:345.175 JLINK_Go()
T58F4 000:345.183   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:348.209 - 3.043ms
T58F4 000:348.231 JLINK_IsHalted()
T58F4 000:348.609 - 0.380ms returns FALSE
T58F4 000:348.619 JLINK_HasError()
T58F4 000:349.690 JLINK_IsHalted()
T58F4 000:352.483   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:352.846 - 3.158ms returns TRUE
T58F4 000:352.854 JLINK_ReadReg(R15 (PC))
T58F4 000:352.860 - 0.008ms returns 0x20000000
T58F4 000:352.866 JLINK_ClrBPEx(BPHandle = 0x00000007)
T58F4 000:352.872 - 0.007ms returns 0x00
T58F4 000:352.877 JLINK_ReadReg(R0)
T58F4 000:352.882 - 0.007ms returns 0x00000000
T58F4 000:352.904 JLINK_HasError()
T58F4 000:352.910 JLINK_WriteReg(R0, 0x08001000)
T58F4 000:352.916 - 0.007ms returns 0
T58F4 000:352.921 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:352.926 - 0.007ms returns 0
T58F4 000:352.932 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:352.937 - 0.007ms returns 0
T58F4 000:352.942 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:352.947 - 0.007ms returns 0
T58F4 000:352.953 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:352.958 - 0.007ms returns 0
T58F4 000:352.963 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:352.968 - 0.007ms returns 0
T58F4 000:352.974 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:352.979 - 0.007ms returns 0
T58F4 000:352.984 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:352.989 - 0.007ms returns 0
T58F4 000:352.995 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:353.000 - 0.007ms returns 0
T58F4 000:353.006 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:353.010 - 0.007ms returns 0
T58F4 000:353.016 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:353.021 - 0.007ms returns 0
T58F4 000:353.027 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:353.032 - 0.007ms returns 0
T58F4 000:353.037 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:353.042 - 0.007ms returns 0
T58F4 000:353.048 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:353.053 - 0.007ms returns 0
T58F4 000:353.059 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:353.064 - 0.007ms returns 0
T58F4 000:353.069 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:353.074 - 0.007ms returns 0
T58F4 000:353.080 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:353.085 - 0.007ms returns 0
T58F4 000:353.091 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:353.096 - 0.007ms returns 0
T58F4 000:353.101 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:353.106 - 0.007ms returns 0
T58F4 000:353.112 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:353.117 - 0.007ms returns 0
T58F4 000:353.167 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:353.174 - 0.010ms returns 0x00000008
T58F4 000:353.180 JLINK_Go()
T58F4 000:353.189   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:356.198 - 3.026ms
T58F4 000:356.212 JLINK_IsHalted()
T58F4 000:359.014   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:359.388 - 3.178ms returns TRUE
T58F4 000:359.395 JLINK_ReadReg(R15 (PC))
T58F4 000:359.401 - 0.008ms returns 0x20000000
T58F4 000:359.407 JLINK_ClrBPEx(BPHandle = 0x00000008)
T58F4 000:359.412 - 0.007ms returns 0x00
T58F4 000:359.418 JLINK_ReadReg(R0)
T58F4 000:359.423 - 0.007ms returns 0x00000001
T58F4 000:359.429 JLINK_HasError()
T58F4 000:359.435 JLINK_WriteReg(R0, 0x08001000)
T58F4 000:359.440 - 0.007ms returns 0
T58F4 000:359.446 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:359.451 - 0.007ms returns 0
T58F4 000:359.456 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:359.461 - 0.007ms returns 0
T58F4 000:359.467 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:359.472 - 0.007ms returns 0
T58F4 000:359.477 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:359.482 - 0.007ms returns 0
T58F4 000:359.488 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:359.493 - 0.007ms returns 0
T58F4 000:359.499 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:359.503 - 0.007ms returns 0
T58F4 000:359.509 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:359.514 - 0.007ms returns 0
T58F4 000:359.520 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:359.524 - 0.007ms returns 0
T58F4 000:359.530 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:359.535 - 0.007ms returns 0
T58F4 000:359.541 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:359.546 - 0.007ms returns 0
T58F4 000:359.551 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:359.556 - 0.007ms returns 0
T58F4 000:359.562 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:359.567 - 0.007ms returns 0
T58F4 000:359.572 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:359.578 - 0.007ms returns 0
T58F4 000:359.583 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:359.588 - 0.007ms returns 0
T58F4 000:359.594 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:359.599 - 0.007ms returns 0
T58F4 000:359.605 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:359.610 - 0.007ms returns 0
T58F4 000:359.615 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:359.620 - 0.007ms returns 0
T58F4 000:359.626 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:359.631 - 0.007ms returns 0
T58F4 000:359.637 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:359.642 - 0.007ms returns 0
T58F4 000:359.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:359.653 - 0.007ms returns 0x00000009
T58F4 000:359.658 JLINK_Go()
T58F4 000:359.666   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:362.703 - 3.053ms
T58F4 000:362.718 JLINK_IsHalted()
T58F4 000:363.061 - 0.346ms returns FALSE
T58F4 000:363.069 JLINK_HasError()
T58F4 000:365.028 JLINK_IsHalted()
T58F4 000:365.382 - 0.357ms returns FALSE
T58F4 000:365.390 JLINK_HasError()
T58F4 000:367.028 JLINK_IsHalted()
T58F4 000:367.363 - 0.337ms returns FALSE
T58F4 000:367.369 JLINK_HasError()
T58F4 000:369.032 JLINK_IsHalted()
T58F4 000:369.389 - 0.360ms returns FALSE
T58F4 000:369.397 JLINK_HasError()
T58F4 000:371.027 JLINK_IsHalted()
T58F4 000:371.358 - 0.333ms returns FALSE
T58F4 000:371.365 JLINK_HasError()
T58F4 000:373.090 JLINK_IsHalted()
T58F4 000:373.474 - 0.387ms returns FALSE
T58F4 000:373.482 JLINK_HasError()
T58F4 000:375.032 JLINK_IsHalted()
T58F4 000:375.388 - 0.359ms returns FALSE
T58F4 000:375.396 JLINK_HasError()
T58F4 000:377.112 JLINK_IsHalted()
T58F4 000:377.477 - 0.375ms returns FALSE
T58F4 000:377.493 JLINK_HasError()
T58F4 000:379.045 JLINK_IsHalted()
T58F4 000:379.433 - 0.391ms returns FALSE
T58F4 000:379.440 JLINK_HasError()
T58F4 000:381.435 JLINK_IsHalted()
T58F4 000:381.840 - 0.412ms returns FALSE
T58F4 000:381.853 JLINK_HasError()
T58F4 000:383.835 JLINK_IsHalted()
T58F4 000:384.254 - 0.422ms returns FALSE
T58F4 000:384.261 JLINK_HasError()
T58F4 000:385.837 JLINK_IsHalted()
T58F4 000:388.653   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:389.042 - 3.211ms returns TRUE
T58F4 000:389.056 JLINK_ReadReg(R15 (PC))
T58F4 000:389.063 - 0.009ms returns 0x20000000
T58F4 000:389.069 JLINK_ClrBPEx(BPHandle = 0x00000009)
T58F4 000:389.074 - 0.007ms returns 0x00
T58F4 000:389.080 JLINK_ReadReg(R0)
T58F4 000:389.085 - 0.007ms returns 0x00000000
T58F4 000:389.111 JLINK_HasError()
T58F4 000:389.117 JLINK_WriteReg(R0, 0x08001400)
T58F4 000:389.122 - 0.007ms returns 0
T58F4 000:389.128 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:389.133 - 0.007ms returns 0
T58F4 000:389.139 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:389.144 - 0.007ms returns 0
T58F4 000:389.149 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:389.154 - 0.007ms returns 0
T58F4 000:389.160 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:389.164 - 0.007ms returns 0
T58F4 000:389.170 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:389.175 - 0.007ms returns 0
T58F4 000:389.181 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:389.185 - 0.007ms returns 0
T58F4 000:389.191 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:389.196 - 0.007ms returns 0
T58F4 000:389.202 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:389.207 - 0.007ms returns 0
T58F4 000:389.212 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:389.217 - 0.007ms returns 0
T58F4 000:389.223 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:389.228 - 0.007ms returns 0
T58F4 000:389.233 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:389.238 - 0.007ms returns 0
T58F4 000:389.244 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:389.249 - 0.007ms returns 0
T58F4 000:389.255 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:389.260 - 0.007ms returns 0
T58F4 000:389.265 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:389.270 - 0.007ms returns 0
T58F4 000:389.276 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:389.281 - 0.007ms returns 0
T58F4 000:389.287 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:389.292 - 0.007ms returns 0
T58F4 000:389.297 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:389.302 - 0.007ms returns 0
T58F4 000:389.308 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:389.313 - 0.007ms returns 0
T58F4 000:389.319 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:389.324 - 0.007ms returns 0
T58F4 000:389.330 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:389.335 - 0.007ms returns 0x0000000A
T58F4 000:389.341 JLINK_Go()
T58F4 000:389.350   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:392.296 - 2.968ms
T58F4 000:392.316 JLINK_IsHalted()
T58F4 000:392.687 - 0.384ms returns FALSE
T58F4 000:392.708 JLINK_HasError()
T58F4 000:394.347 JLINK_IsHalted()
T58F4 000:397.135   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:397.575 - 3.230ms returns TRUE
T58F4 000:397.583 JLINK_ReadReg(R15 (PC))
T58F4 000:397.589 - 0.009ms returns 0x20000000
T58F4 000:397.596 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T58F4 000:397.601 - 0.007ms returns 0x00
T58F4 000:397.607 JLINK_ReadReg(R0)
T58F4 000:397.612 - 0.007ms returns 0x00000000
T58F4 000:397.646 JLINK_HasError()
T58F4 000:397.653 JLINK_WriteReg(R0, 0x08001800)
T58F4 000:397.658 - 0.007ms returns 0
T58F4 000:397.664 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:397.669 - 0.007ms returns 0
T58F4 000:397.675 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:397.679 - 0.007ms returns 0
T58F4 000:397.685 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:397.690 - 0.007ms returns 0
T58F4 000:397.696 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:397.701 - 0.007ms returns 0
T58F4 000:397.706 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:397.711 - 0.007ms returns 0
T58F4 000:397.717 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:397.722 - 0.007ms returns 0
T58F4 000:397.728 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:397.733 - 0.007ms returns 0
T58F4 000:397.739 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:397.744 - 0.007ms returns 0
T58F4 000:397.749 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:397.754 - 0.007ms returns 0
T58F4 000:397.760 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:397.765 - 0.007ms returns 0
T58F4 000:397.770 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:397.775 - 0.007ms returns 0
T58F4 000:397.781 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:397.786 - 0.007ms returns 0
T58F4 000:397.791 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:397.803 - 0.014ms returns 0
T58F4 000:397.809 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:397.814 - 0.007ms returns 0
T58F4 000:397.820 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:397.824 - 0.007ms returns 0
T58F4 000:397.830 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:397.835 - 0.007ms returns 0
T58F4 000:397.841 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:397.846 - 0.007ms returns 0
T58F4 000:397.852 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:397.857 - 0.007ms returns 0
T58F4 000:397.862 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:397.867 - 0.007ms returns 0
T58F4 000:397.873 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:397.879 - 0.007ms returns 0x0000000B
T58F4 000:397.885 JLINK_Go()
T58F4 000:397.893   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:400.869 - 2.993ms
T58F4 000:400.891 JLINK_IsHalted()
T58F4 000:403.662   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:404.066 - 3.185ms returns TRUE
T58F4 000:404.090 JLINK_ReadReg(R15 (PC))
T58F4 000:404.098 - 0.010ms returns 0x20000000
T58F4 000:404.106 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T58F4 000:404.112 - 0.007ms returns 0x00
T58F4 000:404.117 JLINK_ReadReg(R0)
T58F4 000:404.123 - 0.007ms returns 0x00000001
T58F4 000:404.131 JLINK_HasError()
T58F4 000:404.137 JLINK_WriteReg(R0, 0x08001800)
T58F4 000:404.143 - 0.007ms returns 0
T58F4 000:404.150 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:404.155 - 0.007ms returns 0
T58F4 000:404.161 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:404.166 - 0.007ms returns 0
T58F4 000:404.174 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:404.179 - 0.007ms returns 0
T58F4 000:404.184 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:404.189 - 0.007ms returns 0
T58F4 000:404.197 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:404.202 - 0.007ms returns 0
T58F4 000:404.210 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:404.215 - 0.007ms returns 0
T58F4 000:404.267 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:404.282 - 0.017ms returns 0
T58F4 000:404.289 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:404.294 - 0.007ms returns 0
T58F4 000:404.300 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:404.305 - 0.007ms returns 0
T58F4 000:404.311 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:404.315 - 0.007ms returns 0
T58F4 000:404.321 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:404.326 - 0.007ms returns 0
T58F4 000:404.332 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:404.337 - 0.007ms returns 0
T58F4 000:404.342 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:404.348 - 0.007ms returns 0
T58F4 000:404.353 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:404.358 - 0.007ms returns 0
T58F4 000:404.364 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:404.369 - 0.007ms returns 0
T58F4 000:404.375 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:404.380 - 0.007ms returns 0
T58F4 000:404.385 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:404.390 - 0.007ms returns 0
T58F4 000:404.396 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:404.400 - 0.007ms returns 0
T58F4 000:404.406 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:404.411 - 0.007ms returns 0
T58F4 000:404.417 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:404.423 - 0.008ms returns 0x0000000C
T58F4 000:404.429 JLINK_Go()
T58F4 000:404.438   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:407.415 - 2.989ms
T58F4 000:407.423 JLINK_IsHalted()
T58F4 000:407.750 - 0.330ms returns FALSE
T58F4 000:407.757 JLINK_HasError()
T58F4 000:408.910 JLINK_IsHalted()
T58F4 000:409.297 - 0.392ms returns FALSE
T58F4 000:409.308 JLINK_HasError()
T58F4 000:410.897 JLINK_IsHalted()
T58F4 000:411.269 - 0.380ms returns FALSE
T58F4 000:411.283 JLINK_HasError()
T58F4 000:412.894 JLINK_IsHalted()
T58F4 000:413.265 - 0.374ms returns FALSE
T58F4 000:413.272 JLINK_HasError()
T58F4 000:414.895 JLINK_IsHalted()
T58F4 000:415.264 - 0.373ms returns FALSE
T58F4 000:415.273 JLINK_HasError()
T58F4 000:416.893 JLINK_IsHalted()
T58F4 000:417.246 - 0.357ms returns FALSE
T58F4 000:417.255 JLINK_HasError()
T58F4 000:418.894 JLINK_IsHalted()
T58F4 000:419.266 - 0.379ms returns FALSE
T58F4 000:419.283 JLINK_HasError()
T58F4 000:420.896 JLINK_IsHalted()
T58F4 000:421.242 - 0.348ms returns FALSE
T58F4 000:421.249 JLINK_HasError()
T58F4 000:423.246 JLINK_IsHalted()
T58F4 000:423.624 - 0.383ms returns FALSE
T58F4 000:423.635 JLINK_HasError()
T58F4 000:425.463 JLINK_IsHalted()
T58F4 000:425.833 - 0.372ms returns FALSE
T58F4 000:425.839 JLINK_HasError()
T58F4 000:427.518 JLINK_IsHalted()
T58F4 000:427.858 - 0.343ms returns FALSE
T58F4 000:427.865 JLINK_HasError()
T58F4 000:429.457 JLINK_IsHalted()
T58F4 000:432.287   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:432.658 - 3.203ms returns TRUE
T58F4 000:432.672 JLINK_ReadReg(R15 (PC))
T58F4 000:432.679 - 0.008ms returns 0x20000000
T58F4 000:432.685 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T58F4 000:432.689 - 0.006ms returns 0x00
T58F4 000:432.694 JLINK_ReadReg(R0)
T58F4 000:432.698 - 0.005ms returns 0x00000000
T58F4 000:432.737 JLINK_HasError()
T58F4 000:432.742 JLINK_WriteReg(R0, 0x08001C00)
T58F4 000:432.746 - 0.006ms returns 0
T58F4 000:432.751 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:432.754 - 0.005ms returns 0
T58F4 000:432.759 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:432.762 - 0.005ms returns 0
T58F4 000:432.767 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:432.770 - 0.005ms returns 0
T58F4 000:432.774 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:432.778 - 0.005ms returns 0
T58F4 000:432.782 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:432.786 - 0.005ms returns 0
T58F4 000:432.790 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:432.794 - 0.005ms returns 0
T58F4 000:432.798 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:432.801 - 0.005ms returns 0
T58F4 000:432.806 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:432.809 - 0.005ms returns 0
T58F4 000:432.814 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:432.817 - 0.005ms returns 0
T58F4 000:432.822 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:432.825 - 0.005ms returns 0
T58F4 000:432.830 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:432.833 - 0.005ms returns 0
T58F4 000:432.838 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:432.841 - 0.005ms returns 0
T58F4 000:432.845 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:432.849 - 0.005ms returns 0
T58F4 000:432.857 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:432.863 - 0.009ms returns 0
T58F4 000:432.869 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:432.874 - 0.007ms returns 0
T58F4 000:432.879 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:432.884 - 0.007ms returns 0
T58F4 000:432.890 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:432.895 - 0.007ms returns 0
T58F4 000:432.901 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:432.906 - 0.007ms returns 0
T58F4 000:432.911 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:432.916 - 0.007ms returns 0
T58F4 000:432.922 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:432.928 - 0.008ms returns 0x0000000D
T58F4 000:432.934 JLINK_Go()
T58F4 000:432.943   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:435.973 - 3.048ms
T58F4 000:435.988 JLINK_IsHalted()
T58F4 000:436.321 - 0.335ms returns FALSE
T58F4 000:436.328 JLINK_HasError()
T58F4 000:437.440 JLINK_IsHalted()
T58F4 000:437.771 - 0.334ms returns FALSE
T58F4 000:437.778 JLINK_HasError()
T58F4 000:439.445 JLINK_IsHalted()
T58F4 000:442.261   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:442.625 - 3.185ms returns TRUE
T58F4 000:442.635 JLINK_ReadReg(R15 (PC))
T58F4 000:442.641 - 0.007ms returns 0x20000000
T58F4 000:442.645 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T58F4 000:442.649 - 0.005ms returns 0x00
T58F4 000:442.653 JLINK_ReadReg(R0)
T58F4 000:442.657 - 0.005ms returns 0x00000000
T58F4 000:442.678 JLINK_HasError()
T58F4 000:442.683 JLINK_WriteReg(R0, 0x08002000)
T58F4 000:442.686 - 0.005ms returns 0
T58F4 000:442.691 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:442.694 - 0.005ms returns 0
T58F4 000:442.698 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:442.702 - 0.005ms returns 0
T58F4 000:442.706 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:442.710 - 0.005ms returns 0
T58F4 000:442.715 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:442.718 - 0.005ms returns 0
T58F4 000:442.726 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:442.732 - 0.007ms returns 0
T58F4 000:442.736 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:442.740 - 0.005ms returns 0
T58F4 000:442.744 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:442.747 - 0.005ms returns 0
T58F4 000:442.751 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:442.755 - 0.005ms returns 0
T58F4 000:442.759 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:442.763 - 0.005ms returns 0
T58F4 000:442.767 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:442.771 - 0.005ms returns 0
T58F4 000:442.775 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:442.779 - 0.005ms returns 0
T58F4 000:442.783 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:442.787 - 0.005ms returns 0
T58F4 000:442.791 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:442.795 - 0.005ms returns 0
T58F4 000:442.799 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:442.803 - 0.005ms returns 0
T58F4 000:442.807 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:442.811 - 0.005ms returns 0
T58F4 000:442.815 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:442.819 - 0.005ms returns 0
T58F4 000:442.823 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:442.827 - 0.005ms returns 0
T58F4 000:442.831 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:442.835 - 0.005ms returns 0
T58F4 000:442.839 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:442.843 - 0.005ms returns 0
T58F4 000:442.847 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:442.851 - 0.005ms returns 0x0000000E
T58F4 000:442.855 JLINK_Go()
T58F4 000:442.863   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:445.840 - 2.988ms
T58F4 000:445.848 JLINK_IsHalted()
T58F4 000:448.642   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:449.009 - 3.167ms returns TRUE
T58F4 000:449.022 JLINK_ReadReg(R15 (PC))
T58F4 000:449.029 - 0.009ms returns 0x20000000
T58F4 000:449.035 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T58F4 000:449.041 - 0.007ms returns 0x00
T58F4 000:449.046 JLINK_ReadReg(R0)
T58F4 000:449.051 - 0.007ms returns 0x00000001
T58F4 000:449.058 JLINK_HasError()
T58F4 000:449.063 JLINK_WriteReg(R0, 0x08002000)
T58F4 000:449.069 - 0.007ms returns 0
T58F4 000:449.075 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:449.080 - 0.007ms returns 0
T58F4 000:449.085 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:449.090 - 0.007ms returns 0
T58F4 000:449.096 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:449.101 - 0.007ms returns 0
T58F4 000:449.106 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:449.111 - 0.007ms returns 0
T58F4 000:449.117 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:449.122 - 0.007ms returns 0
T58F4 000:449.128 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:449.133 - 0.007ms returns 0
T58F4 000:449.138 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:449.143 - 0.007ms returns 0
T58F4 000:449.149 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:449.154 - 0.007ms returns 0
T58F4 000:449.159 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:449.164 - 0.007ms returns 0
T58F4 000:449.170 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:449.175 - 0.007ms returns 0
T58F4 000:449.180 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:449.185 - 0.007ms returns 0
T58F4 000:449.191 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:449.196 - 0.007ms returns 0
T58F4 000:449.201 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:449.207 - 0.007ms returns 0
T58F4 000:449.212 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:449.217 - 0.007ms returns 0
T58F4 000:449.223 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:449.228 - 0.007ms returns 0
T58F4 000:449.234 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:449.239 - 0.007ms returns 0
T58F4 000:449.244 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:449.249 - 0.007ms returns 0
T58F4 000:449.255 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:449.260 - 0.007ms returns 0
T58F4 000:449.266 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:449.270 - 0.007ms returns 0
T58F4 000:449.276 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:449.282 - 0.007ms returns 0x0000000F
T58F4 000:449.287 JLINK_Go()
T58F4 000:449.296   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:452.250 - 2.970ms
T58F4 000:452.263 JLINK_IsHalted()
T58F4 000:452.627 - 0.374ms returns FALSE
T58F4 000:452.643 JLINK_HasError()
T58F4 000:453.936 JLINK_IsHalted()
T58F4 000:454.342 - 0.417ms returns FALSE
T58F4 000:454.361 JLINK_HasError()
T58F4 000:455.934 JLINK_IsHalted()
T58F4 000:456.304 - 0.376ms returns FALSE
T58F4 000:456.314 JLINK_HasError()
T58F4 000:457.965 JLINK_IsHalted()
T58F4 000:458.326 - 0.364ms returns FALSE
T58F4 000:458.334 JLINK_HasError()
T58F4 000:459.934 JLINK_IsHalted()
T58F4 000:460.300 - 0.371ms returns FALSE
T58F4 000:460.310 JLINK_HasError()
T58F4 000:462.298 JLINK_IsHalted()
T58F4 000:462.664 - 0.369ms returns FALSE
T58F4 000:462.671 JLINK_HasError()
T58F4 000:464.408 JLINK_IsHalted()
T58F4 000:464.781 - 0.382ms returns FALSE
T58F4 000:464.797 JLINK_HasError()
T58F4 000:466.488 JLINK_IsHalted()
T58F4 000:466.845 - 0.360ms returns FALSE
T58F4 000:466.853 JLINK_HasError()
T58F4 000:468.479 JLINK_IsHalted()
T58F4 000:468.841 - 0.365ms returns FALSE
T58F4 000:468.849 JLINK_HasError()
T58F4 000:470.477 JLINK_IsHalted()
T58F4 000:470.868 - 0.400ms returns FALSE
T58F4 000:470.886 JLINK_HasError()
T58F4 000:472.485 JLINK_IsHalted()
T58F4 000:472.849 - 0.371ms returns FALSE
T58F4 000:472.862 JLINK_HasError()
T58F4 000:474.480 JLINK_IsHalted()
T58F4 000:477.265   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:477.641 - 3.164ms returns TRUE
T58F4 000:477.650 JLINK_ReadReg(R15 (PC))
T58F4 000:477.658 - 0.011ms returns 0x20000000
T58F4 000:477.664 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T58F4 000:477.670 - 0.007ms returns 0x00
T58F4 000:477.676 JLINK_ReadReg(R0)
T58F4 000:477.681 - 0.007ms returns 0x00000000
T58F4 000:477.707 JLINK_HasError()
T58F4 000:477.713 JLINK_WriteReg(R0, 0x08002400)
T58F4 000:477.718 - 0.007ms returns 0
T58F4 000:477.724 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:477.729 - 0.007ms returns 0
T58F4 000:477.735 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:477.740 - 0.007ms returns 0
T58F4 000:477.746 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:477.750 - 0.007ms returns 0
T58F4 000:477.756 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:477.761 - 0.007ms returns 0
T58F4 000:477.767 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:477.771 - 0.007ms returns 0
T58F4 000:477.777 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:477.782 - 0.007ms returns 0
T58F4 000:477.788 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:477.793 - 0.007ms returns 0
T58F4 000:477.798 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:477.803 - 0.007ms returns 0
T58F4 000:477.809 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:477.814 - 0.007ms returns 0
T58F4 000:477.819 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:477.824 - 0.007ms returns 0
T58F4 000:477.830 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:477.835 - 0.007ms returns 0
T58F4 000:477.840 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:477.845 - 0.007ms returns 0
T58F4 000:477.851 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:477.856 - 0.007ms returns 0
T58F4 000:477.862 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:477.867 - 0.007ms returns 0
T58F4 000:477.872 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:477.877 - 0.007ms returns 0
T58F4 000:477.883 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:477.888 - 0.007ms returns 0
T58F4 000:477.893 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:477.898 - 0.007ms returns 0
T58F4 000:477.904 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:477.909 - 0.007ms returns 0
T58F4 000:477.915 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:477.920 - 0.007ms returns 0
T58F4 000:477.925 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:477.931 - 0.007ms returns 0x00000010
T58F4 000:477.937 JLINK_Go()
T58F4 000:477.946   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:480.901 - 2.967ms
T58F4 000:480.909 JLINK_IsHalted()
T58F4 000:481.245 - 0.338ms returns FALSE
T58F4 000:481.252 JLINK_HasError()
T58F4 000:483.008 JLINK_IsHalted()
T58F4 000:485.819   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:486.219 - 3.215ms returns TRUE
T58F4 000:486.228 JLINK_ReadReg(R15 (PC))
T58F4 000:486.234 - 0.007ms returns 0x20000000
T58F4 000:486.239 JLINK_ClrBPEx(BPHandle = 0x00000010)
T58F4 000:486.246 - 0.011ms returns 0x00
T58F4 000:486.253 JLINK_ReadReg(R0)
T58F4 000:486.257 - 0.005ms returns 0x00000000
T58F4 000:486.276 JLINK_HasError()
T58F4 000:486.281 JLINK_WriteReg(R0, 0x08002800)
T58F4 000:486.285 - 0.005ms returns 0
T58F4 000:486.289 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:486.293 - 0.005ms returns 0
T58F4 000:486.297 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:486.301 - 0.005ms returns 0
T58F4 000:486.305 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:486.309 - 0.005ms returns 0
T58F4 000:486.313 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:486.317 - 0.005ms returns 0
T58F4 000:486.321 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:486.324 - 0.005ms returns 0
T58F4 000:486.328 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:486.332 - 0.005ms returns 0
T58F4 000:486.336 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:486.340 - 0.005ms returns 0
T58F4 000:486.344 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:486.347 - 0.005ms returns 0
T58F4 000:486.352 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:486.355 - 0.005ms returns 0
T58F4 000:486.359 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:486.363 - 0.005ms returns 0
T58F4 000:486.367 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:486.371 - 0.005ms returns 0
T58F4 000:486.375 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:486.379 - 0.005ms returns 0
T58F4 000:486.383 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:486.386 - 0.005ms returns 0
T58F4 000:486.391 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:486.394 - 0.005ms returns 0
T58F4 000:486.398 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:486.402 - 0.005ms returns 0
T58F4 000:486.407 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:486.410 - 0.005ms returns 0
T58F4 000:486.414 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:486.418 - 0.005ms returns 0
T58F4 000:486.422 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:486.426 - 0.005ms returns 0
T58F4 000:486.430 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:486.434 - 0.005ms returns 0
T58F4 000:486.438 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:486.442 - 0.005ms returns 0x00000011
T58F4 000:486.446 JLINK_Go()
T58F4 000:486.453   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:489.499 - 3.061ms
T58F4 000:489.513 JLINK_IsHalted()
T58F4 000:492.290   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:492.650 - 3.140ms returns TRUE
T58F4 000:492.658 JLINK_ReadReg(R15 (PC))
T58F4 000:492.665 - 0.009ms returns 0x20000000
T58F4 000:492.671 JLINK_ClrBPEx(BPHandle = 0x00000011)
T58F4 000:492.676 - 0.007ms returns 0x00
T58F4 000:492.682 JLINK_ReadReg(R0)
T58F4 000:492.687 - 0.007ms returns 0x00000001
T58F4 000:492.693 JLINK_HasError()
T58F4 000:492.699 JLINK_WriteReg(R0, 0x08002800)
T58F4 000:492.704 - 0.007ms returns 0
T58F4 000:492.710 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:492.715 - 0.007ms returns 0
T58F4 000:492.721 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:492.726 - 0.007ms returns 0
T58F4 000:492.731 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:492.736 - 0.007ms returns 0
T58F4 000:492.742 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:492.747 - 0.007ms returns 0
T58F4 000:492.752 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:492.757 - 0.007ms returns 0
T58F4 000:492.763 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:492.768 - 0.007ms returns 0
T58F4 000:492.773 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:492.778 - 0.007ms returns 0
T58F4 000:492.784 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:492.789 - 0.007ms returns 0
T58F4 000:492.794 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:492.799 - 0.007ms returns 0
T58F4 000:492.805 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:492.810 - 0.007ms returns 0
T58F4 000:492.816 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:492.820 - 0.007ms returns 0
T58F4 000:492.826 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:492.831 - 0.007ms returns 0
T58F4 000:492.837 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:492.842 - 0.007ms returns 0
T58F4 000:492.847 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:492.852 - 0.007ms returns 0
T58F4 000:492.858 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:492.863 - 0.007ms returns 0
T58F4 000:492.872 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:492.879 - 0.009ms returns 0
T58F4 000:492.885 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:492.889 - 0.007ms returns 0
T58F4 000:492.895 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:492.900 - 0.007ms returns 0
T58F4 000:492.906 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:492.911 - 0.007ms returns 0
T58F4 000:492.916 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:492.922 - 0.007ms returns 0x00000012
T58F4 000:492.927 JLINK_Go()
T58F4 000:492.935   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:495.933 - 3.016ms
T58F4 000:495.950 JLINK_IsHalted()
T58F4 000:496.349 - 0.404ms returns FALSE
T58F4 000:496.360 JLINK_HasError()
T58F4 000:497.469 JLINK_IsHalted()
T58F4 000:497.846 - 0.385ms returns FALSE
T58F4 000:497.860 JLINK_HasError()
T58F4 000:498.969 JLINK_IsHalted()
T58F4 000:499.332 - 0.366ms returns FALSE
T58F4 000:499.340 JLINK_HasError()
T58F4 000:501.075 JLINK_IsHalted()
T58F4 000:501.468 - 0.402ms returns FALSE
T58F4 000:501.484 JLINK_HasError()
T58F4 000:503.070 JLINK_IsHalted()
T58F4 000:503.417 - 0.349ms returns FALSE
T58F4 000:503.422 JLINK_HasError()
T58F4 000:505.071 JLINK_IsHalted()
T58F4 000:505.431 - 0.363ms returns FALSE
T58F4 000:505.439 JLINK_HasError()
T58F4 000:507.070 JLINK_IsHalted()
T58F4 000:507.446 - 0.384ms returns FALSE
T58F4 000:507.460 JLINK_HasError()
T58F4 000:509.069 JLINK_IsHalted()
T58F4 000:509.456 - 0.392ms returns FALSE
T58F4 000:509.466 JLINK_HasError()
T58F4 000:511.581 JLINK_IsHalted()
T58F4 000:511.934 - 0.361ms returns FALSE
T58F4 000:511.947 JLINK_HasError()
T58F4 000:513.593 JLINK_IsHalted()
T58F4 000:513.961 - 0.371ms returns FALSE
T58F4 000:513.969 JLINK_HasError()
T58F4 000:515.575 JLINK_IsHalted()
T58F4 000:515.913 - 0.340ms returns FALSE
T58F4 000:515.920 JLINK_HasError()
T58F4 000:517.583 JLINK_IsHalted()
T58F4 000:517.932 - 0.352ms returns FALSE
T58F4 000:517.940 JLINK_HasError()
T58F4 000:519.073 JLINK_IsHalted()
T58F4 000:521.842   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:522.235 - 3.165ms returns TRUE
T58F4 000:522.244 JLINK_ReadReg(R15 (PC))
T58F4 000:522.251 - 0.009ms returns 0x20000000
T58F4 000:522.257 JLINK_ClrBPEx(BPHandle = 0x00000012)
T58F4 000:522.262 - 0.007ms returns 0x00
T58F4 000:522.268 JLINK_ReadReg(R0)
T58F4 000:522.273 - 0.007ms returns 0x00000000
T58F4 000:522.306 JLINK_HasError()
T58F4 000:522.312 JLINK_WriteReg(R0, 0x08002C00)
T58F4 000:522.318 - 0.007ms returns 0
T58F4 000:522.323 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:522.328 - 0.007ms returns 0
T58F4 000:522.334 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:522.339 - 0.007ms returns 0
T58F4 000:522.345 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:522.350 - 0.007ms returns 0
T58F4 000:522.355 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:522.360 - 0.007ms returns 0
T58F4 000:522.366 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:522.371 - 0.007ms returns 0
T58F4 000:522.377 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:522.382 - 0.007ms returns 0
T58F4 000:522.387 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:522.392 - 0.007ms returns 0
T58F4 000:522.398 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:522.403 - 0.007ms returns 0
T58F4 000:522.409 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:522.413 - 0.007ms returns 0
T58F4 000:522.419 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:522.424 - 0.007ms returns 0
T58F4 000:522.430 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:522.435 - 0.007ms returns 0
T58F4 000:522.440 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:522.445 - 0.007ms returns 0
T58F4 000:522.451 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:522.456 - 0.007ms returns 0
T58F4 000:522.462 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:522.467 - 0.007ms returns 0
T58F4 000:522.473 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:522.477 - 0.007ms returns 0
T58F4 000:522.483 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:522.488 - 0.007ms returns 0
T58F4 000:522.494 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:522.499 - 0.007ms returns 0
T58F4 000:522.504 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:522.554 - 0.054ms returns 0
T58F4 000:522.563 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:522.568 - 0.007ms returns 0
T58F4 000:522.573 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:522.579 - 0.007ms returns 0x00000013
T58F4 000:522.584 JLINK_Go()
T58F4 000:522.594   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:525.586 - 3.009ms
T58F4 000:525.600 JLINK_IsHalted()
T58F4 000:525.945 - 0.348ms returns FALSE
T58F4 000:525.953 JLINK_HasError()
T58F4 000:527.422 JLINK_IsHalted()
T58F4 000:530.151   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:530.587 - 3.175ms returns TRUE
T58F4 000:530.603 JLINK_ReadReg(R15 (PC))
T58F4 000:530.611 - 0.010ms returns 0x20000000
T58F4 000:530.618 JLINK_ClrBPEx(BPHandle = 0x00000013)
T58F4 000:530.623 - 0.007ms returns 0x00
T58F4 000:530.629 JLINK_ReadReg(R0)
T58F4 000:530.634 - 0.007ms returns 0x00000000
T58F4 000:530.656 JLINK_HasError()
T58F4 000:530.662 JLINK_WriteReg(R0, 0x08003000)
T58F4 000:530.668 - 0.007ms returns 0
T58F4 000:530.673 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:530.678 - 0.007ms returns 0
T58F4 000:530.684 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:530.689 - 0.007ms returns 0
T58F4 000:530.695 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:530.699 - 0.007ms returns 0
T58F4 000:530.705 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:530.710 - 0.007ms returns 0
T58F4 000:530.716 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:530.721 - 0.007ms returns 0
T58F4 000:530.726 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:530.731 - 0.007ms returns 0
T58F4 000:530.737 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:530.742 - 0.007ms returns 0
T58F4 000:530.748 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:530.752 - 0.007ms returns 0
T58F4 000:530.758 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:530.763 - 0.007ms returns 0
T58F4 000:530.769 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:530.774 - 0.007ms returns 0
T58F4 000:530.779 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:530.785 - 0.007ms returns 0
T58F4 000:530.790 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:530.795 - 0.007ms returns 0
T58F4 000:530.801 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:530.806 - 0.007ms returns 0
T58F4 000:530.811 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:530.816 - 0.007ms returns 0
T58F4 000:530.822 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:530.827 - 0.007ms returns 0
T58F4 000:530.832 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:530.837 - 0.007ms returns 0
T58F4 000:530.843 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:530.848 - 0.007ms returns 0
T58F4 000:530.854 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:530.859 - 0.007ms returns 0
T58F4 000:530.864 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:530.869 - 0.007ms returns 0
T58F4 000:530.875 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:530.880 - 0.007ms returns 0x00000014
T58F4 000:530.886 JLINK_Go()
T58F4 000:530.895   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:533.977 - 3.099ms
T58F4 000:533.998 JLINK_IsHalted()
T58F4 000:536.832   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:537.192 - 3.196ms returns TRUE
T58F4 000:537.204 JLINK_ReadReg(R15 (PC))
T58F4 000:537.211 - 0.009ms returns 0x20000000
T58F4 000:537.219 JLINK_ClrBPEx(BPHandle = 0x00000014)
T58F4 000:537.224 - 0.007ms returns 0x00
T58F4 000:537.232 JLINK_ReadReg(R0)
T58F4 000:537.237 - 0.007ms returns 0x00000001
T58F4 000:537.269 JLINK_HasError()
T58F4 000:537.276 JLINK_WriteReg(R0, 0x08003000)
T58F4 000:537.281 - 0.008ms returns 0
T58F4 000:537.287 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:537.292 - 0.007ms returns 0
T58F4 000:537.298 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:537.303 - 0.007ms returns 0
T58F4 000:537.308 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:537.313 - 0.007ms returns 0
T58F4 000:537.319 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:537.324 - 0.007ms returns 0
T58F4 000:537.330 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:537.334 - 0.007ms returns 0
T58F4 000:537.340 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:537.345 - 0.007ms returns 0
T58F4 000:537.351 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:537.356 - 0.031ms returns 0
T58F4 000:537.388 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:537.393 - 0.007ms returns 0
T58F4 000:537.399 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:537.404 - 0.007ms returns 0
T58F4 000:537.410 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:537.415 - 0.007ms returns 0
T58F4 000:537.420 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:537.425 - 0.007ms returns 0
T58F4 000:537.431 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:537.436 - 0.007ms returns 0
T58F4 000:537.441 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:537.447 - 0.007ms returns 0
T58F4 000:537.452 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:537.457 - 0.007ms returns 0
T58F4 000:537.463 JLINK_WriteReg(R15 (PC), 0x200000B6)
T58F4 000:537.468 - 0.007ms returns 0
T58F4 000:537.473 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:537.478 - 0.007ms returns 0
T58F4 000:537.484 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:537.489 - 0.007ms returns 0
T58F4 000:537.495 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:537.499 - 0.007ms returns 0
T58F4 000:537.505 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:537.510 - 0.007ms returns 0
T58F4 000:537.516 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:537.522 - 0.007ms returns 0x00000015
T58F4 000:537.527 JLINK_Go()
T58F4 000:537.538   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:540.580 - 3.056ms
T58F4 000:540.588 JLINK_IsHalted()
T58F4 000:540.910 - 0.324ms returns FALSE
T58F4 000:540.917 JLINK_HasError()
T58F4 000:542.611 JLINK_IsHalted()
T58F4 000:542.968 - 0.360ms returns FALSE
T58F4 000:542.975 JLINK_HasError()
T58F4 000:544.617 JLINK_IsHalted()
T58F4 000:544.988 - 0.375ms returns FALSE
T58F4 000:544.998 JLINK_HasError()
T58F4 000:546.610 JLINK_IsHalted()
T58F4 000:546.979 - 0.371ms returns FALSE
T58F4 000:546.986 JLINK_HasError()
T58F4 000:548.642 JLINK_IsHalted()
T58F4 000:549.009 - 0.375ms returns FALSE
T58F4 000:549.023 JLINK_HasError()
T58F4 000:550.615 JLINK_IsHalted()
T58F4 000:550.965 - 0.355ms returns FALSE
T58F4 000:550.975 JLINK_HasError()
T58F4 000:552.610 JLINK_IsHalted()
T58F4 000:552.946 - 0.343ms returns FALSE
T58F4 000:552.959 JLINK_HasError()
T58F4 000:554.613 JLINK_IsHalted()
T58F4 000:554.974 - 0.363ms returns FALSE
T58F4 000:554.981 JLINK_HasError()
T58F4 000:556.633 JLINK_IsHalted()
T58F4 000:556.987 - 0.361ms returns FALSE
T58F4 000:557.000 JLINK_HasError()
T58F4 000:558.128 JLINK_IsHalted()
T58F4 000:558.475 - 0.350ms returns FALSE
T58F4 000:558.482 JLINK_HasError()
T58F4 000:559.620 JLINK_IsHalted()
T58F4 000:559.948 - 0.330ms returns FALSE
T58F4 000:559.954 JLINK_HasError()
T58F4 000:561.682 JLINK_IsHalted()
T58F4 000:562.072 - 0.398ms returns FALSE
T58F4 000:562.086 JLINK_HasError()
T58F4 000:563.677 JLINK_IsHalted()
T58F4 000:566.461   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:566.862 - 3.187ms returns TRUE
T58F4 000:566.869 JLINK_ReadReg(R15 (PC))
T58F4 000:566.877 - 0.010ms returns 0x20000000
T58F4 000:566.883 JLINK_ClrBPEx(BPHandle = 0x00000015)
T58F4 000:566.888 - 0.007ms returns 0x00
T58F4 000:566.894 JLINK_ReadReg(R0)
T58F4 000:566.900 - 0.007ms returns 0x00000000
T58F4 000:566.932 JLINK_HasError()
T58F4 000:566.938 JLINK_WriteReg(R0, 0x08003400)
T58F4 000:566.943 - 0.007ms returns 0
T58F4 000:566.949 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:566.954 - 0.007ms returns 0
T58F4 000:566.960 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:566.965 - 0.007ms returns 0
T58F4 000:566.971 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:566.975 - 0.007ms returns 0
T58F4 000:566.981 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:566.986 - 0.007ms returns 0
T58F4 000:566.992 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:566.997 - 0.007ms returns 0
T58F4 000:567.002 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:567.007 - 0.007ms returns 0
T58F4 000:567.013 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:567.018 - 0.007ms returns 0
T58F4 000:567.023 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:567.028 - 0.007ms returns 0
T58F4 000:567.034 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:567.039 - 0.007ms returns 0
T58F4 000:567.045 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:567.087 - 0.048ms returns 0
T58F4 000:567.096 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:567.101 - 0.007ms returns 0
T58F4 000:567.107 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:567.112 - 0.007ms returns 0
T58F4 000:567.117 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:567.123 - 0.007ms returns 0
T58F4 000:567.128 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:567.133 - 0.007ms returns 0
T58F4 000:567.139 JLINK_WriteReg(R15 (PC), 0x20000020)
T58F4 000:567.144 - 0.007ms returns 0
T58F4 000:567.150 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:567.155 - 0.007ms returns 0
T58F4 000:567.160 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:567.165 - 0.007ms returns 0
T58F4 000:567.171 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:567.176 - 0.007ms returns 0
T58F4 000:567.181 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:567.186 - 0.007ms returns 0
T58F4 000:567.192 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:567.198 - 0.008ms returns 0x00000016
T58F4 000:567.204 JLINK_Go()
T58F4 000:567.213   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:570.218 - 3.021ms
T58F4 000:570.231 JLINK_IsHalted()
T58F4 000:570.588 - 0.366ms returns FALSE
T58F4 000:570.604 JLINK_HasError()
T58F4 000:571.675 JLINK_IsHalted()
T58F4 000:574.453   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:574.875 - 3.207ms returns TRUE
T58F4 000:574.888 JLINK_ReadReg(R15 (PC))
T58F4 000:574.895 - 0.010ms returns 0x20000000
T58F4 000:574.901 JLINK_ClrBPEx(BPHandle = 0x00000016)
T58F4 000:574.907 - 0.007ms returns 0x00
T58F4 000:574.912 JLINK_ReadReg(R0)
T58F4 000:574.917 - 0.007ms returns 0x00000000
T58F4 000:574.955 JLINK_HasError()
T58F4 000:574.962 JLINK_WriteReg(R0, 0x00000001)
T58F4 000:574.967 - 0.007ms returns 0
T58F4 000:574.973 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:574.978 - 0.007ms returns 0
T58F4 000:574.984 JLINK_WriteReg(R2, 0x000000FF)
T58F4 000:574.988 - 0.007ms returns 0
T58F4 000:574.994 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:574.999 - 0.007ms returns 0
T58F4 000:575.005 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:575.010 - 0.007ms returns 0
T58F4 000:575.015 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:575.020 - 0.007ms returns 0
T58F4 000:575.026 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:575.031 - 0.007ms returns 0
T58F4 000:575.036 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:575.041 - 0.007ms returns 0
T58F4 000:575.047 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:575.052 - 0.007ms returns 0
T58F4 000:575.057 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:575.062 - 0.007ms returns 0
T58F4 000:575.068 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:575.073 - 0.007ms returns 0
T58F4 000:575.078 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:575.083 - 0.007ms returns 0
T58F4 000:575.089 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:575.094 - 0.007ms returns 0
T58F4 000:575.099 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:575.105 - 0.007ms returns 0
T58F4 000:575.110 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:575.115 - 0.007ms returns 0
T58F4 000:575.121 JLINK_WriteReg(R15 (PC), 0x2000006A)
T58F4 000:575.126 - 0.007ms returns 0
T58F4 000:575.131 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:575.136 - 0.007ms returns 0
T58F4 000:575.142 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:575.147 - 0.007ms returns 0
T58F4 000:575.153 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:575.158 - 0.007ms returns 0
T58F4 000:575.163 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:575.168 - 0.007ms returns 0
T58F4 000:575.174 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:575.179 - 0.007ms returns 0x00000017
T58F4 000:575.185 JLINK_Go()
T58F4 000:575.193   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:578.188 - 3.011ms
T58F4 000:578.201 JLINK_IsHalted()
T58F4 000:580.991   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:581.380 - 3.184ms returns TRUE
T58F4 000:581.390 JLINK_ReadReg(R15 (PC))
T58F4 000:581.396 - 0.007ms returns 0x20000000
T58F4 000:581.400 JLINK_ClrBPEx(BPHandle = 0x00000017)
T58F4 000:581.404 - 0.005ms returns 0x00
T58F4 000:581.409 JLINK_ReadReg(R0)
T58F4 000:581.412 - 0.005ms returns 0x00000000
T58F4 000:632.981 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T58F4 000:632.994   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T58F4 000:633.013   CPU_WriteMem(356 bytes @ 0x20000000)
T58F4 000:637.157 - 4.194ms returns 0x164
T58F4 000:637.206 JLINK_HasError()
T58F4 000:637.214 JLINK_WriteReg(R0, 0x08000000)
T58F4 000:637.223 - 0.011ms returns 0
T58F4 000:637.229 JLINK_WriteReg(R1, 0x00B71B00)
T58F4 000:637.235 - 0.007ms returns 0
T58F4 000:637.240 JLINK_WriteReg(R2, 0x00000002)
T58F4 000:637.245 - 0.007ms returns 0
T58F4 000:637.251 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:637.256 - 0.007ms returns 0
T58F4 000:637.262 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:637.267 - 0.007ms returns 0
T58F4 000:637.273 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:637.278 - 0.007ms returns 0
T58F4 000:637.283 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:637.288 - 0.007ms returns 0
T58F4 000:637.294 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:637.299 - 0.007ms returns 0
T58F4 000:637.304 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:637.309 - 0.007ms returns 0
T58F4 000:637.315 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:637.320 - 0.007ms returns 0
T58F4 000:637.325 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:637.330 - 0.007ms returns 0
T58F4 000:637.336 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:637.341 - 0.007ms returns 0
T58F4 000:637.347 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:637.352 - 0.007ms returns 0
T58F4 000:637.357 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:637.363 - 0.008ms returns 0
T58F4 000:637.369 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:637.374 - 0.007ms returns 0
T58F4 000:637.380 JLINK_WriteReg(R15 (PC), 0x20000038)
T58F4 000:637.385 - 0.007ms returns 0
T58F4 000:637.391 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:637.396 - 0.007ms returns 0
T58F4 000:637.402 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:637.406 - 0.007ms returns 0
T58F4 000:637.412 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:637.417 - 0.007ms returns 0
T58F4 000:637.423 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:637.428 - 0.007ms returns 0
T58F4 000:637.434 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:637.443   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:637.925 - 0.504ms returns 0x00000018
T58F4 000:637.945 JLINK_Go()
T58F4 000:637.954   CPU_WriteMem(2 bytes @ 0x20000000)
T58F4 000:638.347   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:641.454 - 3.521ms
T58F4 000:641.485 JLINK_IsHalted()
T58F4 000:644.395   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:644.804 - 3.325ms returns TRUE
T58F4 000:644.826 JLINK_ReadReg(R15 (PC))
T58F4 000:644.836 - 0.013ms returns 0x20000000
T58F4 000:644.846 JLINK_ClrBPEx(BPHandle = 0x00000018)
T58F4 000:644.852 - 0.008ms returns 0x00
T58F4 000:644.861 JLINK_ReadReg(R0)
T58F4 000:644.867 - 0.008ms returns 0x00000000
T58F4 000:644.925 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:644.933   Data:  C0 07 00 20 89 01 00 08 91 01 00 08 93 01 00 08 ...
T58F4 000:644.949   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:652.237 - 7.325ms returns 0x29C
T58F4 000:652.257 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:652.262   Data:  01 21 88 07 01 F0 CF F9 1F BD 00 00 00 08 01 40 ...
T58F4 000:652.278   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:656.432 - 4.183ms returns 0x164
T58F4 000:656.447 JLINK_HasError()
T58F4 000:656.454 JLINK_WriteReg(R0, 0x08000000)
T58F4 000:656.463 - 0.010ms returns 0
T58F4 000:656.469 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:656.474 - 0.007ms returns 0
T58F4 000:656.480 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:656.485 - 0.007ms returns 0
T58F4 000:656.490 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:656.495 - 0.007ms returns 0
T58F4 000:656.501 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:656.506 - 0.007ms returns 0
T58F4 000:656.512 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:656.517 - 0.007ms returns 0
T58F4 000:656.522 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:656.527 - 0.007ms returns 0
T58F4 000:656.533 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:656.538 - 0.007ms returns 0
T58F4 000:656.544 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:656.602 - 0.060ms returns 0
T58F4 000:656.608 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:656.613 - 0.007ms returns 0
T58F4 000:656.619 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:656.624 - 0.007ms returns 0
T58F4 000:656.630 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:656.635 - 0.007ms returns 0
T58F4 000:656.640 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:656.645 - 0.007ms returns 0
T58F4 000:656.651 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:656.656 - 0.007ms returns 0
T58F4 000:656.662 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:656.667 - 0.007ms returns 0
T58F4 000:656.673 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:656.678 - 0.007ms returns 0
T58F4 000:656.683 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:656.688 - 0.007ms returns 0
T58F4 000:656.694 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:656.699 - 0.007ms returns 0
T58F4 000:656.705 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:656.710 - 0.007ms returns 0
T58F4 000:656.715 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:656.720 - 0.007ms returns 0
T58F4 000:656.726 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:656.732 - 0.008ms returns 0x00000019
T58F4 000:656.738 JLINK_Go()
T58F4 000:656.749   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:659.732 - 3.002ms
T58F4 000:659.746 JLINK_IsHalted()
T58F4 000:660.082 - 0.338ms returns FALSE
T58F4 000:660.089 JLINK_HasError()
T58F4 000:661.729 JLINK_IsHalted()
T58F4 000:662.067 - 0.340ms returns FALSE
T58F4 000:662.072 JLINK_HasError()
T58F4 000:663.731 JLINK_IsHalted()
T58F4 000:664.093 - 0.364ms returns FALSE
T58F4 000:664.100 JLINK_HasError()
T58F4 000:665.734 JLINK_IsHalted()
T58F4 000:666.095 - 0.364ms returns FALSE
T58F4 000:666.104 JLINK_HasError()
T58F4 000:667.736 JLINK_IsHalted()
T58F4 000:668.100 - 0.370ms returns FALSE
T58F4 000:668.113 JLINK_HasError()
T58F4 000:669.729 JLINK_IsHalted()
T58F4 000:670.197 - 0.471ms returns FALSE
T58F4 000:670.204 JLINK_HasError()
T58F4 000:671.735 JLINK_IsHalted()
T58F4 000:672.098 - 0.365ms returns FALSE
T58F4 000:672.105 JLINK_HasError()
T58F4 000:673.735 JLINK_IsHalted()
T58F4 000:674.097 - 0.369ms returns FALSE
T58F4 000:674.110 JLINK_HasError()
T58F4 000:675.731 JLINK_IsHalted()
T58F4 000:676.100 - 0.371ms returns FALSE
T58F4 000:676.107 JLINK_HasError()
T58F4 000:678.103 JLINK_IsHalted()
T58F4 000:678.469 - 0.372ms returns FALSE
T58F4 000:678.481 JLINK_HasError()
T58F4 000:680.111 JLINK_IsHalted()
T58F4 000:680.449 - 0.340ms returns FALSE
T58F4 000:680.456 JLINK_HasError()
T58F4 000:682.105 JLINK_IsHalted()
T58F4 000:682.442 - 0.339ms returns FALSE
T58F4 000:682.449 JLINK_HasError()
T58F4 000:684.109 JLINK_IsHalted()
T58F4 000:684.484 - 0.377ms returns FALSE
T58F4 000:684.490 JLINK_HasError()
T58F4 000:686.106 JLINK_IsHalted()
T58F4 000:686.446 - 0.342ms returns FALSE
T58F4 000:686.453 JLINK_HasError()
T58F4 000:688.133 JLINK_IsHalted()
T58F4 000:688.479 - 0.349ms returns FALSE
T58F4 000:688.487 JLINK_HasError()
T58F4 000:690.104 JLINK_IsHalted()
T58F4 000:690.514 - 0.420ms returns FALSE
T58F4 000:690.532 JLINK_HasError()
T58F4 000:692.614 JLINK_IsHalted()
T58F4 000:695.422   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:695.802 - 3.198ms returns TRUE
T58F4 000:695.830 JLINK_ReadReg(R15 (PC))
T58F4 000:695.840 - 0.012ms returns 0x20000000
T58F4 000:695.846 JLINK_ClrBPEx(BPHandle = 0x00000019)
T58F4 000:695.853 - 0.008ms returns 0x00
T58F4 000:695.859 JLINK_ReadReg(R0)
T58F4 000:695.865 - 0.008ms returns 0x00000000
T58F4 000:695.964 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:695.980   Data:  13 4F 7E 60 12 E0 00 F4 80 16 B6 F5 80 1F 06 D1 ...
T58F4 000:695.996   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:703.289 - 7.336ms returns 0x29C
T58F4 000:703.306 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:703.312   Data:  60 06 04 0E 02 20 01 F0 F7 FA 01 20 0A 49 C1 F8 ...
T58F4 000:703.326   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:707.436 - 4.138ms returns 0x164
T58F4 000:707.452 JLINK_HasError()
T58F4 000:707.459 JLINK_WriteReg(R0, 0x08000400)
T58F4 000:707.474 - 0.017ms returns 0
T58F4 000:707.480 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:707.485 - 0.007ms returns 0
T58F4 000:707.491 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:707.496 - 0.007ms returns 0
T58F4 000:707.501 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:707.506 - 0.007ms returns 0
T58F4 000:707.512 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:707.517 - 0.007ms returns 0
T58F4 000:707.522 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:707.527 - 0.007ms returns 0
T58F4 000:707.533 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:707.538 - 0.007ms returns 0
T58F4 000:707.544 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:707.548 - 0.007ms returns 0
T58F4 000:707.554 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:707.559 - 0.007ms returns 0
T58F4 000:707.565 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:707.570 - 0.007ms returns 0
T58F4 000:707.575 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:707.580 - 0.007ms returns 0
T58F4 000:707.586 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:707.591 - 0.007ms returns 0
T58F4 000:707.596 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:707.601 - 0.007ms returns 0
T58F4 000:707.607 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:707.613 - 0.007ms returns 0
T58F4 000:707.618 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:707.623 - 0.007ms returns 0
T58F4 000:707.629 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:707.634 - 0.007ms returns 0
T58F4 000:707.639 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:707.644 - 0.007ms returns 0
T58F4 000:707.650 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:707.655 - 0.007ms returns 0
T58F4 000:707.661 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:707.666 - 0.007ms returns 0
T58F4 000:707.671 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:707.676 - 0.007ms returns 0
T58F4 000:707.682 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:707.687 - 0.007ms returns 0x0000001A
T58F4 000:707.693 JLINK_Go()
T58F4 000:707.704   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:710.669 - 2.978ms
T58F4 000:710.677 JLINK_IsHalted()
T58F4 000:711.030 - 0.356ms returns FALSE
T58F4 000:711.037 JLINK_HasError()
T58F4 000:713.019 JLINK_IsHalted()
T58F4 000:713.387 - 0.375ms returns FALSE
T58F4 000:713.401 JLINK_HasError()
T58F4 000:714.527 JLINK_IsHalted()
T58F4 000:714.888 - 0.367ms returns FALSE
T58F4 000:714.901 JLINK_HasError()
T58F4 000:716.526 JLINK_IsHalted()
T58F4 000:716.891 - 0.368ms returns FALSE
T58F4 000:716.898 JLINK_HasError()
T58F4 000:718.524 JLINK_IsHalted()
T58F4 000:718.859 - 0.337ms returns FALSE
T58F4 000:718.866 JLINK_HasError()
T58F4 000:720.533 JLINK_IsHalted()
T58F4 000:720.895 - 0.369ms returns FALSE
T58F4 000:720.907 JLINK_HasError()
T58F4 000:722.526 JLINK_IsHalted()
T58F4 000:722.896 - 0.372ms returns FALSE
T58F4 000:722.903 JLINK_HasError()
T58F4 000:724.894 JLINK_IsHalted()
T58F4 000:725.296 - 0.411ms returns FALSE
T58F4 000:725.312 JLINK_HasError()
T58F4 000:726.901 JLINK_IsHalted()
T58F4 000:727.263 - 0.365ms returns FALSE
T58F4 000:727.271 JLINK_HasError()
T58F4 000:728.981 JLINK_IsHalted()
T58F4 000:729.315 - 0.337ms returns FALSE
T58F4 000:729.322 JLINK_HasError()
T58F4 000:730.982 JLINK_IsHalted()
T58F4 000:731.375 - 0.402ms returns FALSE
T58F4 000:731.392 JLINK_HasError()
T58F4 000:732.984 JLINK_IsHalted()
T58F4 000:733.363 - 0.385ms returns FALSE
T58F4 000:733.375 JLINK_HasError()
T58F4 000:734.982 JLINK_IsHalted()
T58F4 000:735.351 - 0.378ms returns FALSE
T58F4 000:735.367 JLINK_HasError()
T58F4 000:737.007 JLINK_IsHalted()
T58F4 000:737.397 - 0.392ms returns FALSE
T58F4 000:737.403 JLINK_HasError()
T58F4 000:738.987 JLINK_IsHalted()
T58F4 000:739.346 - 0.366ms returns FALSE
T58F4 000:739.359 JLINK_HasError()
T58F4 000:740.981 JLINK_IsHalted()
T58F4 000:741.321 - 0.343ms returns FALSE
T58F4 000:741.328 JLINK_HasError()
T58F4 000:742.981 JLINK_IsHalted()
T58F4 000:745.947   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:746.320 - 3.346ms returns TRUE
T58F4 000:746.333 JLINK_ReadReg(R15 (PC))
T58F4 000:746.342 - 0.011ms returns 0x20000000
T58F4 000:746.348 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T58F4 000:746.359 - 0.013ms returns 0x00
T58F4 000:746.365 JLINK_ReadReg(R0)
T58F4 000:746.370 - 0.007ms returns 0x00000000
T58F4 000:746.404 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:746.409   Data:  06 F8 FF F7 19 FD 10 BD 00 04 00 40 00 08 00 40 ...
T58F4 000:746.420   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:753.692 - 7.297ms returns 0x29C
T58F4 000:753.715 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:753.720   Data:  FF F7 E7 FC 01 22 40 21 04 48 FF F7 E2 FC 01 22 ...
T58F4 000:753.732   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:757.851 - 4.139ms returns 0x164
T58F4 000:757.863 JLINK_HasError()
T58F4 000:757.870 JLINK_WriteReg(R0, 0x08000800)
T58F4 000:757.876 - 0.008ms returns 0
T58F4 000:757.884 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:757.889 - 0.007ms returns 0
T58F4 000:757.897 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:757.902 - 0.007ms returns 0
T58F4 000:757.909 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:757.914 - 0.007ms returns 0
T58F4 000:757.922 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:757.927 - 0.007ms returns 0
T58F4 000:757.933 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:757.937 - 0.007ms returns 0
T58F4 000:757.945 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:757.950 - 0.007ms returns 0
T58F4 000:757.958 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:757.963 - 0.007ms returns 0
T58F4 000:757.970 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:757.975 - 0.007ms returns 0
T58F4 000:758.013 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:758.019 - 0.008ms returns 0
T58F4 000:758.025 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:758.030 - 0.007ms returns 0
T58F4 000:758.035 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:758.040 - 0.007ms returns 0
T58F4 000:758.046 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:758.051 - 0.007ms returns 0
T58F4 000:758.056 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:758.062 - 0.008ms returns 0
T58F4 000:758.068 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:758.073 - 0.007ms returns 0
T58F4 000:758.078 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:758.083 - 0.007ms returns 0
T58F4 000:758.089 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:758.094 - 0.007ms returns 0
T58F4 000:758.100 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:758.104 - 0.007ms returns 0
T58F4 000:758.110 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:758.115 - 0.007ms returns 0
T58F4 000:758.121 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:758.126 - 0.007ms returns 0
T58F4 000:758.132 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:758.137 - 0.007ms returns 0x0000001B
T58F4 000:758.143 JLINK_Go()
T58F4 000:758.152   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:761.113 - 2.976ms
T58F4 000:761.125 JLINK_IsHalted()
T58F4 000:761.448 - 0.325ms returns FALSE
T58F4 000:761.455 JLINK_HasError()
T58F4 000:762.502 JLINK_IsHalted()
T58F4 000:762.863 - 0.368ms returns FALSE
T58F4 000:762.876 JLINK_HasError()
T58F4 000:764.496 JLINK_IsHalted()
T58F4 000:764.931 - 0.438ms returns FALSE
T58F4 000:764.938 JLINK_HasError()
T58F4 000:765.979 JLINK_IsHalted()
T58F4 000:766.310 - 0.334ms returns FALSE
T58F4 000:766.317 JLINK_HasError()
T58F4 000:767.991 JLINK_IsHalted()
T58F4 000:768.384 - 0.400ms returns FALSE
T58F4 000:768.397 JLINK_HasError()
T58F4 000:769.982 JLINK_IsHalted()
T58F4 000:770.327 - 0.350ms returns FALSE
T58F4 000:770.336 JLINK_HasError()
T58F4 000:771.981 JLINK_IsHalted()
T58F4 000:772.320 - 0.345ms returns FALSE
T58F4 000:772.330 JLINK_HasError()
T58F4 000:773.982 JLINK_IsHalted()
T58F4 000:774.332 - 0.357ms returns FALSE
T58F4 000:774.345 JLINK_HasError()
T58F4 000:775.980 JLINK_IsHalted()
T58F4 000:776.315 - 0.338ms returns FALSE
T58F4 000:776.322 JLINK_HasError()
T58F4 000:777.983 JLINK_IsHalted()
T58F4 000:778.315 - 0.334ms returns FALSE
T58F4 000:778.322 JLINK_HasError()
T58F4 000:779.984 JLINK_IsHalted()
T58F4 000:780.338 - 0.360ms returns FALSE
T58F4 000:780.351 JLINK_HasError()
T58F4 000:781.981 JLINK_IsHalted()
T58F4 000:782.315 - 0.337ms returns FALSE
T58F4 000:782.322 JLINK_HasError()
T58F4 000:783.989 JLINK_IsHalted()
T58F4 000:784.335 - 0.348ms returns FALSE
T58F4 000:784.347 JLINK_HasError()
T58F4 000:786.089 JLINK_IsHalted()
T58F4 000:786.480 - 0.399ms returns FALSE
T58F4 000:786.495 JLINK_HasError()
T58F4 000:788.477 JLINK_IsHalted()
T58F4 000:788.857 - 0.383ms returns FALSE
T58F4 000:788.864 JLINK_HasError()
T58F4 000:790.855 JLINK_IsHalted()
T58F4 000:791.202 - 0.350ms returns FALSE
T58F4 000:791.209 JLINK_HasError()
T58F4 000:792.862 JLINK_IsHalted()
T58F4 000:795.660   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:796.046 - 3.187ms returns TRUE
T58F4 000:796.055 JLINK_ReadReg(R15 (PC))
T58F4 000:796.062 - 0.010ms returns 0x20000000
T58F4 000:796.068 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T58F4 000:796.073 - 0.007ms returns 0x00
T58F4 000:796.079 JLINK_ReadReg(R0)
T58F4 000:796.084 - 0.007ms returns 0x00000000
T58F4 000:796.116 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:796.121   Data:  2D E9 F0 41 07 46 0E 46 15 46 00 24 07 E0 2A 5D ...
T58F4 000:796.131   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:803.302 - 7.197ms returns 0x29C
T58F4 000:803.319 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:803.325   Data:  DD 40 45 60 0F 4D 6D 68 05 F4 E0 61 09 0A 10 4D ...
T58F4 000:803.338   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:807.441 - 4.129ms returns 0x164
T58F4 000:807.456 JLINK_HasError()
T58F4 000:807.463 JLINK_WriteReg(R0, 0x08000C00)
T58F4 000:807.470 - 0.010ms returns 0
T58F4 000:807.477 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:807.482 - 0.007ms returns 0
T58F4 000:807.488 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:807.493 - 0.007ms returns 0
T58F4 000:807.498 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:807.503 - 0.007ms returns 0
T58F4 000:807.509 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:807.514 - 0.007ms returns 0
T58F4 000:807.520 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:807.525 - 0.007ms returns 0
T58F4 000:807.530 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:807.535 - 0.007ms returns 0
T58F4 000:807.541 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:807.546 - 0.007ms returns 0
T58F4 000:807.551 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:807.556 - 0.007ms returns 0
T58F4 000:807.562 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:807.567 - 0.007ms returns 0
T58F4 000:807.573 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:807.578 - 0.007ms returns 0
T58F4 000:807.583 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:807.588 - 0.007ms returns 0
T58F4 000:807.594 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:807.599 - 0.007ms returns 0
T58F4 000:807.604 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:807.610 - 0.007ms returns 0
T58F4 000:807.615 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:807.620 - 0.007ms returns 0
T58F4 000:807.626 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:807.631 - 0.007ms returns 0
T58F4 000:807.637 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:807.642 - 0.007ms returns 0
T58F4 000:807.647 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:807.652 - 0.007ms returns 0
T58F4 000:807.658 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:807.663 - 0.007ms returns 0
T58F4 000:807.668 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:807.673 - 0.007ms returns 0
T58F4 000:807.679 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:807.685 - 0.008ms returns 0x0000001C
T58F4 000:807.691 JLINK_Go()
T58F4 000:807.702   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:810.801 - 3.118ms
T58F4 000:810.825 JLINK_IsHalted()
T58F4 000:811.192 - 0.370ms returns FALSE
T58F4 000:811.203 JLINK_HasError()
T58F4 000:812.505 JLINK_IsHalted()
T58F4 000:812.870 - 0.367ms returns FALSE
T58F4 000:812.876 JLINK_HasError()
T58F4 000:814.432 JLINK_IsHalted()
T58F4 000:814.791 - 0.366ms returns FALSE
T58F4 000:814.803 JLINK_HasError()
T58F4 000:816.413 JLINK_IsHalted()
T58F4 000:816.777 - 0.370ms returns FALSE
T58F4 000:816.790 JLINK_HasError()
T58F4 000:818.410 JLINK_IsHalted()
T58F4 000:818.780 - 0.373ms returns FALSE
T58F4 000:818.788 JLINK_HasError()
T58F4 000:820.410 JLINK_IsHalted()
T58F4 000:820.747 - 0.344ms returns FALSE
T58F4 000:820.763 JLINK_HasError()
T58F4 000:822.414 JLINK_IsHalted()
T58F4 000:822.774 - 0.362ms returns FALSE
T58F4 000:822.782 JLINK_HasError()
T58F4 000:824.409 JLINK_IsHalted()
T58F4 000:824.775 - 0.368ms returns FALSE
T58F4 000:824.781 JLINK_HasError()
T58F4 000:826.417 JLINK_IsHalted()
T58F4 000:826.768 - 0.354ms returns FALSE
T58F4 000:826.775 JLINK_HasError()
T58F4 000:828.420 JLINK_IsHalted()
T58F4 000:828.784 - 0.371ms returns FALSE
T58F4 000:828.798 JLINK_HasError()
T58F4 000:830.412 JLINK_IsHalted()
T58F4 000:830.907 - 0.503ms returns FALSE
T58F4 000:830.921 JLINK_HasError()
T58F4 000:832.906 JLINK_IsHalted()
T58F4 000:833.278 - 0.375ms returns FALSE
T58F4 000:833.286 JLINK_HasError()
T58F4 000:835.305 JLINK_IsHalted()
T58F4 000:835.665 - 0.363ms returns FALSE
T58F4 000:835.673 JLINK_HasError()
T58F4 000:837.283 JLINK_IsHalted()
T58F4 000:837.642 - 0.362ms returns FALSE
T58F4 000:837.649 JLINK_HasError()
T58F4 000:839.278 JLINK_IsHalted()
T58F4 000:839.617 - 0.341ms returns FALSE
T58F4 000:839.624 JLINK_HasError()
T58F4 000:841.295 JLINK_IsHalted()
T58F4 000:844.104   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:844.465 - 3.173ms returns TRUE
T58F4 000:844.473 JLINK_ReadReg(R15 (PC))
T58F4 000:844.481 - 0.009ms returns 0x20000000
T58F4 000:844.487 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T58F4 000:844.492 - 0.007ms returns 0x00
T58F4 000:844.498 JLINK_ReadReg(R0)
T58F4 000:844.503 - 0.007ms returns 0x00000000
T58F4 000:844.531 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:844.537   Data:  00 28 F9 D0 09 48 40 68 20 F0 03 00 07 49 48 60 ...
T58F4 000:844.547   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:851.832 - 7.310ms returns 0x29C
T58F4 000:851.848 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:851.854   Data:  08 21 19 48 00 F0 5E FA 08 21 17 48 00 F0 A2 FA ...
T58F4 000:851.867   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:855.971 - 4.131ms returns 0x164
T58F4 000:855.987 JLINK_HasError()
T58F4 000:855.993 JLINK_WriteReg(R0, 0x08001000)
T58F4 000:856.001 - 0.010ms returns 0
T58F4 000:856.007 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:856.012 - 0.007ms returns 0
T58F4 000:856.018 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:856.023 - 0.007ms returns 0
T58F4 000:856.029 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:856.033 - 0.007ms returns 0
T58F4 000:856.039 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:856.044 - 0.007ms returns 0
T58F4 000:856.050 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:856.055 - 0.007ms returns 0
T58F4 000:856.060 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:856.065 - 0.007ms returns 0
T58F4 000:856.071 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:856.076 - 0.007ms returns 0
T58F4 000:856.082 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:856.087 - 0.007ms returns 0
T58F4 000:856.093 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:856.098 - 0.007ms returns 0
T58F4 000:856.103 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:856.108 - 0.007ms returns 0
T58F4 000:856.114 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:856.119 - 0.007ms returns 0
T58F4 000:856.124 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:856.129 - 0.007ms returns 0
T58F4 000:856.135 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:856.141 - 0.008ms returns 0
T58F4 000:856.146 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:856.151 - 0.007ms returns 0
T58F4 000:856.157 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:856.162 - 0.007ms returns 0
T58F4 000:856.168 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:856.173 - 0.007ms returns 0
T58F4 000:856.179 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:856.184 - 0.007ms returns 0
T58F4 000:856.189 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:856.194 - 0.007ms returns 0
T58F4 000:856.200 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:856.205 - 0.007ms returns 0
T58F4 000:856.211 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:856.217 - 0.007ms returns 0x0000001D
T58F4 000:856.222 JLINK_Go()
T58F4 000:856.232   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:859.250 - 3.036ms
T58F4 000:859.265 JLINK_IsHalted()
T58F4 000:859.626 - 0.368ms returns FALSE
T58F4 000:859.639 JLINK_HasError()
T58F4 000:861.625 JLINK_IsHalted()
T58F4 000:862.025 - 0.410ms returns FALSE
T58F4 000:862.051 JLINK_HasError()
T58F4 000:863.625 JLINK_IsHalted()
T58F4 000:863.966 - 0.343ms returns FALSE
T58F4 000:863.974 JLINK_HasError()
T58F4 000:865.969 JLINK_IsHalted()
T58F4 000:866.324 - 0.359ms returns FALSE
T58F4 000:866.333 JLINK_HasError()
T58F4 000:867.963 JLINK_IsHalted()
T58F4 000:868.323 - 0.362ms returns FALSE
T58F4 000:868.330 JLINK_HasError()
T58F4 000:869.963 JLINK_IsHalted()
T58F4 000:870.363 - 0.409ms returns FALSE
T58F4 000:870.379 JLINK_HasError()
T58F4 000:871.969 JLINK_IsHalted()
T58F4 000:872.319 - 0.353ms returns FALSE
T58F4 000:872.327 JLINK_HasError()
T58F4 000:873.963 JLINK_IsHalted()
T58F4 000:874.302 - 0.341ms returns FALSE
T58F4 000:874.309 JLINK_HasError()
T58F4 000:875.963 JLINK_IsHalted()
T58F4 000:876.319 - 0.358ms returns FALSE
T58F4 000:876.326 JLINK_HasError()
T58F4 000:877.975 JLINK_IsHalted()
T58F4 000:878.346 - 0.373ms returns FALSE
T58F4 000:878.354 JLINK_HasError()
T58F4 000:879.970 JLINK_IsHalted()
T58F4 000:880.319 - 0.351ms returns FALSE
T58F4 000:880.326 JLINK_HasError()
T58F4 000:882.318 JLINK_IsHalted()
T58F4 000:882.656 - 0.341ms returns FALSE
T58F4 000:882.663 JLINK_HasError()
T58F4 000:884.660 JLINK_IsHalted()
T58F4 000:885.037 - 0.380ms returns FALSE
T58F4 000:885.045 JLINK_HasError()
T58F4 000:886.660 JLINK_IsHalted()
T58F4 000:887.017 - 0.360ms returns FALSE
T58F4 000:887.030 JLINK_HasError()
T58F4 000:888.659 JLINK_IsHalted()
T58F4 000:889.027 - 0.376ms returns FALSE
T58F4 000:889.042 JLINK_HasError()
T58F4 000:890.659 JLINK_IsHalted()
T58F4 000:893.468   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:893.899 - 3.249ms returns TRUE
T58F4 000:893.916 JLINK_ReadReg(R15 (PC))
T58F4 000:893.925 - 0.011ms returns 0x20000000
T58F4 000:893.931 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T58F4 000:893.941 - 0.011ms returns 0x00
T58F4 000:893.947 JLINK_ReadReg(R0)
T58F4 000:893.952 - 0.008ms returns 0x00000000
T58F4 000:893.988 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:893.993   Data:  10 B5 4F F4 80 41 08 48 FF F7 2F F8 4F F4 00 51 ...
T58F4 000:894.005   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:901.337 - 7.370ms returns 0x29C
T58F4 000:901.366 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:901.370   Data:  01 17 3A 43 4F F6 DD 77 3B 40 4F F6 FF 77 07 EA ...
T58F4 000:901.387   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:905.513 - 4.158ms returns 0x164
T58F4 000:905.532 JLINK_HasError()
T58F4 000:905.540 JLINK_WriteReg(R0, 0x08001400)
T58F4 000:905.549 - 0.011ms returns 0
T58F4 000:905.555 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:905.560 - 0.007ms returns 0
T58F4 000:905.566 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:905.571 - 0.007ms returns 0
T58F4 000:905.576 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:905.581 - 0.007ms returns 0
T58F4 000:905.587 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:905.592 - 0.007ms returns 0
T58F4 000:905.597 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:905.602 - 0.007ms returns 0
T58F4 000:905.608 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:905.613 - 0.007ms returns 0
T58F4 000:905.618 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:905.623 - 0.007ms returns 0
T58F4 000:905.629 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:905.634 - 0.007ms returns 0
T58F4 000:905.640 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:905.645 - 0.007ms returns 0
T58F4 000:905.651 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:905.655 - 0.007ms returns 0
T58F4 000:905.661 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:905.667 - 0.007ms returns 0
T58F4 000:905.672 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:905.677 - 0.007ms returns 0
T58F4 000:905.683 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:905.688 - 0.008ms returns 0
T58F4 000:905.694 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:905.699 - 0.007ms returns 0
T58F4 000:905.705 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:905.710 - 0.007ms returns 0
T58F4 000:905.715 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:905.724 - 0.013ms returns 0
T58F4 000:905.732 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:905.737 - 0.007ms returns 0
T58F4 000:905.742 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:905.747 - 0.007ms returns 0
T58F4 000:905.753 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:905.758 - 0.007ms returns 0
T58F4 000:905.764 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:905.770 - 0.008ms returns 0x0000001E
T58F4 000:905.776 JLINK_Go()
T58F4 000:905.788   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:908.883 - 3.118ms
T58F4 000:908.901 JLINK_IsHalted()
T58F4 000:909.255 - 0.360ms returns FALSE
T58F4 000:909.268 JLINK_HasError()
T58F4 000:911.291 JLINK_IsHalted()
T58F4 000:911.634 - 0.345ms returns FALSE
T58F4 000:911.639 JLINK_HasError()
T58F4 000:913.301 JLINK_IsHalted()
T58F4 000:913.699 - 0.405ms returns FALSE
T58F4 000:913.713 JLINK_HasError()
T58F4 000:915.290 JLINK_IsHalted()
T58F4 000:915.635 - 0.347ms returns FALSE
T58F4 000:915.642 JLINK_HasError()
T58F4 000:917.294 JLINK_IsHalted()
T58F4 000:917.634 - 0.342ms returns FALSE
T58F4 000:917.641 JLINK_HasError()
T58F4 000:919.298 JLINK_IsHalted()
T58F4 000:919.660 - 0.369ms returns FALSE
T58F4 000:919.673 JLINK_HasError()
T58F4 000:921.294 JLINK_IsHalted()
T58F4 000:921.649 - 0.362ms returns FALSE
T58F4 000:921.662 JLINK_HasError()
T58F4 000:923.294 JLINK_IsHalted()
T58F4 000:923.635 - 0.344ms returns FALSE
T58F4 000:923.643 JLINK_HasError()
T58F4 000:925.308 JLINK_IsHalted()
T58F4 000:925.662 - 0.359ms returns FALSE
T58F4 000:925.672 JLINK_HasError()
T58F4 000:927.293 JLINK_IsHalted()
T58F4 000:927.650 - 0.363ms returns FALSE
T58F4 000:927.662 JLINK_HasError()
T58F4 000:929.295 JLINK_IsHalted()
T58F4 000:929.640 - 0.348ms returns FALSE
T58F4 000:929.647 JLINK_HasError()
T58F4 000:931.295 JLINK_IsHalted()
T58F4 000:931.661 - 0.369ms returns FALSE
T58F4 000:931.669 JLINK_HasError()
T58F4 000:933.659 JLINK_IsHalted()
T58F4 000:934.025 - 0.373ms returns FALSE
T58F4 000:934.037 JLINK_HasError()
T58F4 000:935.662 JLINK_IsHalted()
T58F4 000:935.995 - 0.336ms returns FALSE
T58F4 000:936.001 JLINK_HasError()
T58F4 000:937.996 JLINK_IsHalted()
T58F4 000:938.368 - 0.378ms returns FALSE
T58F4 000:938.389 JLINK_HasError()
T58F4 000:939.995 JLINK_IsHalted()
T58F4 000:942.788   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:943.150 - 3.157ms returns TRUE
T58F4 000:943.158 JLINK_ReadReg(R15 (PC))
T58F4 000:943.166 - 0.010ms returns 0x20000000
T58F4 000:943.172 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T58F4 000:943.177 - 0.007ms returns 0x00
T58F4 000:943.183 JLINK_ReadReg(R0)
T58F4 000:943.188 - 0.007ms returns 0x00000000
T58F4 000:943.224 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:943.230   Data:  03 EA 02 23 19 43 81 83 70 47 81 87 70 47 A0 F8 ...
T58F4 000:943.241   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 000:950.576 - 7.365ms returns 0x29C
T58F4 000:950.597 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 000:950.603   Data:  07 FA 06 F5 01 2C 01 D1 0C 30 04 E0 02 2C 01 D1 ...
T58F4 000:950.617   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 000:954.753 - 4.164ms returns 0x164
T58F4 000:954.768 JLINK_HasError()
T58F4 000:954.775 JLINK_WriteReg(R0, 0x08001800)
T58F4 000:954.783 - 0.010ms returns 0
T58F4 000:954.789 JLINK_WriteReg(R1, 0x00000400)
T58F4 000:954.794 - 0.007ms returns 0
T58F4 000:954.800 JLINK_WriteReg(R2, 0x20000164)
T58F4 000:954.805 - 0.007ms returns 0
T58F4 000:954.811 JLINK_WriteReg(R3, 0x00000000)
T58F4 000:954.816 - 0.007ms returns 0
T58F4 000:954.821 JLINK_WriteReg(R4, 0x00000000)
T58F4 000:954.826 - 0.007ms returns 0
T58F4 000:954.832 JLINK_WriteReg(R5, 0x00000000)
T58F4 000:954.837 - 0.007ms returns 0
T58F4 000:954.842 JLINK_WriteReg(R6, 0x00000000)
T58F4 000:954.847 - 0.007ms returns 0
T58F4 000:954.853 JLINK_WriteReg(R7, 0x00000000)
T58F4 000:954.858 - 0.007ms returns 0
T58F4 000:954.864 JLINK_WriteReg(R8, 0x00000000)
T58F4 000:954.869 - 0.007ms returns 0
T58F4 000:954.874 JLINK_WriteReg(R9, 0x20000160)
T58F4 000:954.879 - 0.007ms returns 0
T58F4 000:954.885 JLINK_WriteReg(R10, 0x00000000)
T58F4 000:954.898 - 0.015ms returns 0
T58F4 000:954.904 JLINK_WriteReg(R11, 0x00000000)
T58F4 000:954.909 - 0.007ms returns 0
T58F4 000:954.914 JLINK_WriteReg(R12, 0x00000000)
T58F4 000:954.919 - 0.007ms returns 0
T58F4 000:954.925 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 000:954.931 - 0.008ms returns 0
T58F4 000:954.936 JLINK_WriteReg(R14, 0x20000001)
T58F4 000:954.941 - 0.007ms returns 0
T58F4 000:954.947 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 000:954.952 - 0.007ms returns 0
T58F4 000:954.958 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 000:954.963 - 0.007ms returns 0
T58F4 000:954.969 JLINK_WriteReg(MSP, 0x20001000)
T58F4 000:954.974 - 0.007ms returns 0
T58F4 000:954.979 JLINK_WriteReg(PSP, 0x20001000)
T58F4 000:954.984 - 0.007ms returns 0
T58F4 000:954.990 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 000:954.995 - 0.007ms returns 0
T58F4 000:955.001 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 000:955.006 - 0.008ms returns 0x0000001F
T58F4 000:955.012 JLINK_Go()
T58F4 000:955.023   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 000:958.081 - 3.076ms
T58F4 000:958.094 JLINK_IsHalted()
T58F4 000:958.422 - 0.330ms returns FALSE
T58F4 000:958.428 JLINK_HasError()
T58F4 000:959.668 JLINK_IsHalted()
T58F4 000:960.001 - 0.335ms returns FALSE
T58F4 000:960.007 JLINK_HasError()
T58F4 000:961.691 JLINK_IsHalted()
T58F4 000:962.056 - 0.375ms returns FALSE
T58F4 000:962.072 JLINK_HasError()
T58F4 000:963.733 JLINK_IsHalted()
T58F4 000:964.107 - 0.380ms returns FALSE
T58F4 000:964.124 JLINK_HasError()
T58F4 000:965.667 JLINK_IsHalted()
T58F4 000:965.999 - 0.335ms returns FALSE
T58F4 000:966.006 JLINK_HasError()
T58F4 000:967.703 JLINK_IsHalted()
T58F4 000:968.082 - 0.387ms returns FALSE
T58F4 000:968.096 JLINK_HasError()
T58F4 000:969.671 JLINK_IsHalted()
T58F4 000:970.067 - 0.403ms returns FALSE
T58F4 000:970.081 JLINK_HasError()
T58F4 000:971.669 JLINK_IsHalted()
T58F4 000:972.044 - 0.382ms returns FALSE
T58F4 000:972.057 JLINK_HasError()
T58F4 000:973.695 JLINK_IsHalted()
T58F4 000:974.061 - 0.374ms returns FALSE
T58F4 000:974.075 JLINK_HasError()
T58F4 000:975.668 JLINK_IsHalted()
T58F4 000:976.033 - 0.367ms returns FALSE
T58F4 000:976.039 JLINK_HasError()
T58F4 000:977.672 JLINK_IsHalted()
T58F4 000:978.007 - 0.337ms returns FALSE
T58F4 000:978.014 JLINK_HasError()
T58F4 000:980.011 JLINK_IsHalted()
T58F4 000:980.367 - 0.358ms returns FALSE
T58F4 000:980.375 JLINK_HasError()
T58F4 000:982.011 JLINK_IsHalted()
T58F4 000:982.369 - 0.359ms returns FALSE
T58F4 000:982.374 JLINK_HasError()
T58F4 000:984.012 JLINK_IsHalted()
T58F4 000:984.372 - 0.363ms returns FALSE
T58F4 000:984.379 JLINK_HasError()
T58F4 000:986.041 JLINK_IsHalted()
T58F4 000:986.401 - 0.368ms returns FALSE
T58F4 000:986.415 JLINK_HasError()
T58F4 000:988.016 JLINK_IsHalted()
T58F4 000:988.376 - 0.366ms returns FALSE
T58F4 000:988.388 JLINK_HasError()
T58F4 000:990.010 JLINK_IsHalted()
T58F4 000:992.884   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 000:993.291 - 3.290ms returns TRUE
T58F4 000:993.307 JLINK_ReadReg(R15 (PC))
T58F4 000:993.317 - 0.011ms returns 0x20000000
T58F4 000:993.323 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T58F4 000:993.328 - 0.007ms returns 0x00
T58F4 000:993.334 JLINK_ReadReg(R0)
T58F4 000:993.339 - 0.007ms returns 0x00000000
T58F4 000:993.379 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 000:993.384   Data:  AD F8 0E 00 01 A9 0F 48 FF F7 5C FF 01 21 0D 48 ...
T58F4 000:993.397   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:000.709 - 7.341ms returns 0x29C
T58F4 001:000.727 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:000.732   Data:  4F F0 7E 51 01 20 FE F7 B7 FC 04 E0 02 A2 01 21 ...
T58F4 001:000.746   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:004.884 - 4.174ms returns 0x164
T58F4 001:004.909 JLINK_HasError()
T58F4 001:004.917 JLINK_WriteReg(R0, 0x08001C00)
T58F4 001:004.926 - 0.012ms returns 0
T58F4 001:004.932 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:004.938 - 0.008ms returns 0
T58F4 001:004.945 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:004.959 - 0.016ms returns 0
T58F4 001:004.965 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:004.971 - 0.008ms returns 0
T58F4 001:004.977 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:004.985 - 0.011ms returns 0
T58F4 001:004.992 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:004.997 - 0.008ms returns 0
T58F4 001:005.003 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:005.009 - 0.008ms returns 0
T58F4 001:005.015 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:005.032 - 0.020ms returns 0
T58F4 001:005.039 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:005.044 - 0.008ms returns 0
T58F4 001:005.050 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:005.056 - 0.008ms returns 0
T58F4 001:005.063 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:005.068 - 0.008ms returns 0
T58F4 001:005.074 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:005.080 - 0.008ms returns 0
T58F4 001:005.086 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:005.092 - 0.008ms returns 0
T58F4 001:005.098 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:005.104 - 0.008ms returns 0
T58F4 001:005.110 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:005.116 - 0.007ms returns 0
T58F4 001:005.122 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:005.127 - 0.008ms returns 0
T58F4 001:005.134 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:005.139 - 0.008ms returns 0
T58F4 001:005.146 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:005.151 - 0.008ms returns 0
T58F4 001:005.157 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:005.163 - 0.008ms returns 0
T58F4 001:005.169 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:005.174 - 0.008ms returns 0
T58F4 001:005.181 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:005.187 - 0.009ms returns 0x00000020
T58F4 001:005.194 JLINK_Go()
T58F4 001:005.206   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:008.340 - 3.153ms
T58F4 001:008.354 JLINK_IsHalted()
T58F4 001:008.725 - 0.378ms returns FALSE
T58F4 001:008.737 JLINK_HasError()
T58F4 001:010.032 JLINK_IsHalted()
T58F4 001:010.402 - 0.375ms returns FALSE
T58F4 001:010.413 JLINK_HasError()
T58F4 001:012.031 JLINK_IsHalted()
T58F4 001:012.407 - 0.382ms returns FALSE
T58F4 001:012.420 JLINK_HasError()
T58F4 001:013.536 JLINK_IsHalted()
T58F4 001:013.878 - 0.344ms returns FALSE
T58F4 001:013.884 JLINK_HasError()
T58F4 001:015.534 JLINK_IsHalted()
T58F4 001:015.893 - 0.361ms returns FALSE
T58F4 001:015.898 JLINK_HasError()
T58F4 001:017.534 JLINK_IsHalted()
T58F4 001:017.904 - 0.377ms returns FALSE
T58F4 001:017.917 JLINK_HasError()
T58F4 001:019.551 JLINK_IsHalted()
T58F4 001:019.935 - 0.395ms returns FALSE
T58F4 001:019.957 JLINK_HasError()
T58F4 001:021.531 JLINK_IsHalted()
T58F4 001:021.873 - 0.344ms returns FALSE
T58F4 001:021.878 JLINK_HasError()
T58F4 001:023.541 JLINK_IsHalted()
T58F4 001:023.912 - 0.375ms returns FALSE
T58F4 001:023.921 JLINK_HasError()
T58F4 001:025.538 JLINK_IsHalted()
T58F4 001:025.886 - 0.352ms returns FALSE
T58F4 001:025.894 JLINK_HasError()
T58F4 001:027.884 JLINK_IsHalted()
T58F4 001:028.222 - 0.340ms returns FALSE
T58F4 001:028.229 JLINK_HasError()
T58F4 001:029.887 JLINK_IsHalted()
T58F4 001:030.285 - 0.405ms returns FALSE
T58F4 001:030.298 JLINK_HasError()
T58F4 001:032.011 JLINK_IsHalted()
T58F4 001:032.373 - 0.365ms returns FALSE
T58F4 001:032.381 JLINK_HasError()
T58F4 001:034.008 JLINK_IsHalted()
T58F4 001:034.417 - 0.423ms returns FALSE
T58F4 001:034.438 JLINK_HasError()
T58F4 001:036.014 JLINK_IsHalted()
T58F4 001:036.364 - 0.352ms returns FALSE
T58F4 001:036.370 JLINK_HasError()
T58F4 001:038.008 JLINK_IsHalted()
T58F4 001:038.346 - 0.340ms returns FALSE
T58F4 001:038.353 JLINK_HasError()
T58F4 001:040.010 JLINK_IsHalted()
T58F4 001:042.973   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:043.416 - 3.411ms returns TRUE
T58F4 001:043.433 JLINK_ReadReg(R15 (PC))
T58F4 001:043.441 - 0.009ms returns 0x20000000
T58F4 001:043.447 JLINK_ClrBPEx(BPHandle = 0x00000020)
T58F4 001:043.452 - 0.006ms returns 0x00
T58F4 001:043.456 JLINK_ReadReg(R0)
T58F4 001:043.460 - 0.005ms returns 0x00000000
T58F4 001:043.504 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:043.511   Data:  AE 47 ED 3F 84 D7 77 41 00 00 E0 3F 10 B5 91 EA ...
T58F4 001:043.521   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:050.831 - 7.341ms returns 0x29C
T58F4 001:050.853 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:050.859   Data:  9E EA C4 7F 02 D4 F0 BC BD E8 00 81 24 42 38 D4 ...
T58F4 001:050.874   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:054.966 - 4.122ms returns 0x164
T58F4 001:054.982 JLINK_HasError()
T58F4 001:054.989 JLINK_WriteReg(R0, 0x08002000)
T58F4 001:054.997 - 0.010ms returns 0
T58F4 001:055.003 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:055.008 - 0.007ms returns 0
T58F4 001:055.014 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:055.019 - 0.007ms returns 0
T58F4 001:055.025 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:055.030 - 0.007ms returns 0
T58F4 001:055.035 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:055.040 - 0.007ms returns 0
T58F4 001:055.046 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:055.051 - 0.007ms returns 0
T58F4 001:055.057 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:055.062 - 0.007ms returns 0
T58F4 001:055.068 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:055.072 - 0.007ms returns 0
T58F4 001:055.078 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:055.083 - 0.007ms returns 0
T58F4 001:055.089 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:055.094 - 0.007ms returns 0
T58F4 001:055.100 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:055.104 - 0.007ms returns 0
T58F4 001:055.110 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:055.115 - 0.007ms returns 0
T58F4 001:055.121 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:055.126 - 0.007ms returns 0
T58F4 001:055.132 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:055.137 - 0.007ms returns 0
T58F4 001:055.143 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:055.148 - 0.007ms returns 0
T58F4 001:055.153 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:055.159 - 0.007ms returns 0
T58F4 001:055.164 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:055.169 - 0.007ms returns 0
T58F4 001:055.175 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:055.180 - 0.007ms returns 0
T58F4 001:055.185 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:055.190 - 0.007ms returns 0
T58F4 001:055.196 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:055.201 - 0.007ms returns 0
T58F4 001:055.207 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:055.213 - 0.008ms returns 0x00000021
T58F4 001:055.219 JLINK_Go()
T58F4 001:055.229   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:058.217 - 3.002ms
T58F4 001:058.226 JLINK_IsHalted()
T58F4 001:058.724 - 0.508ms returns FALSE
T58F4 001:058.741 JLINK_HasError()
T58F4 001:060.637 JLINK_IsHalted()
T58F4 001:061.022 - 0.394ms returns FALSE
T58F4 001:061.037 JLINK_HasError()
T58F4 001:062.729 JLINK_IsHalted()
T58F4 001:063.069 - 0.343ms returns FALSE
T58F4 001:063.077 JLINK_HasError()
T58F4 001:064.740 JLINK_IsHalted()
T58F4 001:065.104 - 0.372ms returns FALSE
T58F4 001:065.119 JLINK_HasError()
T58F4 001:066.732 JLINK_IsHalted()
T58F4 001:067.118 - 0.393ms returns FALSE
T58F4 001:067.131 JLINK_HasError()
T58F4 001:068.728 JLINK_IsHalted()
T58F4 001:069.066 - 0.340ms returns FALSE
T58F4 001:069.078 JLINK_HasError()
T58F4 001:070.727 JLINK_IsHalted()
T58F4 001:071.116 - 0.396ms returns FALSE
T58F4 001:071.129 JLINK_HasError()
T58F4 001:073.113 JLINK_IsHalted()
T58F4 001:073.476 - 0.370ms returns FALSE
T58F4 001:073.488 JLINK_HasError()
T58F4 001:075.113 JLINK_IsHalted()
T58F4 001:075.492 - 0.382ms returns FALSE
T58F4 001:075.499 JLINK_HasError()
T58F4 001:077.496 JLINK_IsHalted()
T58F4 001:077.882 - 0.389ms returns FALSE
T58F4 001:077.890 JLINK_HasError()
T58F4 001:079.493 JLINK_IsHalted()
T58F4 001:079.828 - 0.338ms returns FALSE
T58F4 001:079.835 JLINK_HasError()
T58F4 001:081.496 JLINK_IsHalted()
T58F4 001:081.880 - 0.390ms returns FALSE
T58F4 001:081.892 JLINK_HasError()
T58F4 001:083.499 JLINK_IsHalted()
T58F4 001:083.875 - 0.379ms returns FALSE
T58F4 001:083.883 JLINK_HasError()
T58F4 001:085.492 JLINK_IsHalted()
T58F4 001:085.830 - 0.342ms returns FALSE
T58F4 001:085.839 JLINK_HasError()
T58F4 001:087.494 JLINK_IsHalted()
T58F4 001:087.831 - 0.339ms returns FALSE
T58F4 001:087.837 JLINK_HasError()
T58F4 001:089.515 JLINK_IsHalted()
T58F4 001:092.282   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:092.660 - 3.148ms returns TRUE
T58F4 001:092.668 JLINK_ReadReg(R15 (PC))
T58F4 001:092.675 - 0.009ms returns 0x20000000
T58F4 001:092.681 JLINK_ClrBPEx(BPHandle = 0x00000021)
T58F4 001:092.686 - 0.007ms returns 0x00
T58F4 001:092.692 JLINK_ReadReg(R0)
T58F4 001:092.697 - 0.007ms returns 0x00000000
T58F4 001:092.723 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:092.728   Data:  8F 8F 8E 8E 8D 8C 8C 8B 8B 8A 89 89 88 88 87 87 ...
T58F4 001:092.738   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:100.028 - 7.316ms returns 0x29C
T58F4 001:100.056 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:100.061   Data:  00 1F 0C D8 0E D1 4F EA D1 7C 15 F5 00 1F 0C EB ...
T58F4 001:100.074   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:104.162 - 4.115ms returns 0x164
T58F4 001:104.185 JLINK_HasError()
T58F4 001:104.226 JLINK_WriteReg(R0, 0x08002400)
T58F4 001:104.242 - 0.018ms returns 0
T58F4 001:104.249 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:104.254 - 0.007ms returns 0
T58F4 001:104.260 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:104.265 - 0.007ms returns 0
T58F4 001:104.271 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:104.276 - 0.007ms returns 0
T58F4 001:104.281 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:104.286 - 0.007ms returns 0
T58F4 001:104.292 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:104.297 - 0.007ms returns 0
T58F4 001:104.302 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:104.307 - 0.007ms returns 0
T58F4 001:104.313 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:104.318 - 0.007ms returns 0
T58F4 001:104.324 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:104.329 - 0.007ms returns 0
T58F4 001:104.334 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:104.339 - 0.007ms returns 0
T58F4 001:104.345 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:104.350 - 0.007ms returns 0
T58F4 001:104.356 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:104.361 - 0.007ms returns 0
T58F4 001:104.366 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:104.371 - 0.007ms returns 0
T58F4 001:104.377 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:104.382 - 0.007ms returns 0
T58F4 001:104.388 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:104.393 - 0.007ms returns 0
T58F4 001:104.399 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:104.404 - 0.007ms returns 0
T58F4 001:104.410 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:104.415 - 0.007ms returns 0
T58F4 001:104.420 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:104.425 - 0.007ms returns 0
T58F4 001:104.431 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:104.436 - 0.007ms returns 0
T58F4 001:104.442 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:104.447 - 0.007ms returns 0
T58F4 001:104.453 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:104.458 - 0.008ms returns 0x00000022
T58F4 001:104.464 JLINK_Go()
T58F4 001:104.475   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:107.450 - 2.994ms
T58F4 001:107.465 JLINK_IsHalted()
T58F4 001:107.831 - 0.369ms returns FALSE
T58F4 001:107.838 JLINK_HasError()
T58F4 001:109.151 JLINK_IsHalted()
T58F4 001:109.480 - 0.330ms returns FALSE
T58F4 001:109.485 JLINK_HasError()
T58F4 001:111.152 JLINK_IsHalted()
T58F4 001:111.519 - 0.375ms returns FALSE
T58F4 001:111.534 JLINK_HasError()
T58F4 001:113.171 JLINK_IsHalted()
T58F4 001:113.623 - 0.458ms returns FALSE
T58F4 001:113.634 JLINK_HasError()
T58F4 001:115.153 JLINK_IsHalted()
T58F4 001:115.519 - 0.371ms returns FALSE
T58F4 001:115.528 JLINK_HasError()
T58F4 001:117.517 JLINK_IsHalted()
T58F4 001:117.882 - 0.367ms returns FALSE
T58F4 001:117.889 JLINK_HasError()
T58F4 001:119.521 JLINK_IsHalted()
T58F4 001:119.895 - 0.377ms returns FALSE
T58F4 001:119.903 JLINK_HasError()
T58F4 001:120.960 JLINK_IsHalted()
T58F4 001:121.321 - 0.363ms returns FALSE
T58F4 001:121.326 JLINK_HasError()
T58F4 001:122.960 JLINK_IsHalted()
T58F4 001:123.312 - 0.358ms returns FALSE
T58F4 001:123.324 JLINK_HasError()
T58F4 001:124.961 JLINK_IsHalted()
T58F4 001:125.339 - 0.387ms returns FALSE
T58F4 001:125.358 JLINK_HasError()
T58F4 001:126.961 JLINK_IsHalted()
T58F4 001:127.493 - 0.537ms returns FALSE
T58F4 001:127.502 JLINK_HasError()
T58F4 001:128.965 JLINK_IsHalted()
T58F4 001:129.315 - 0.352ms returns FALSE
T58F4 001:129.322 JLINK_HasError()
T58F4 001:130.961 JLINK_IsHalted()
T58F4 001:131.379 - 0.428ms returns FALSE
T58F4 001:131.396 JLINK_HasError()
T58F4 001:132.966 JLINK_IsHalted()
T58F4 001:133.335 - 0.372ms returns FALSE
T58F4 001:133.341 JLINK_HasError()
T58F4 001:134.961 JLINK_IsHalted()
T58F4 001:135.324 - 0.371ms returns FALSE
T58F4 001:135.339 JLINK_HasError()
T58F4 001:136.963 JLINK_IsHalted()
T58F4 001:137.310 - 0.350ms returns FALSE
T58F4 001:137.318 JLINK_HasError()
T58F4 001:139.310 JLINK_IsHalted()
T58F4 001:142.074   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:142.439 - 3.139ms returns TRUE
T58F4 001:142.457 JLINK_ReadReg(R15 (PC))
T58F4 001:142.469 - 0.015ms returns 0x20000000
T58F4 001:142.478 JLINK_ClrBPEx(BPHandle = 0x00000022)
T58F4 001:142.485 - 0.009ms returns 0x00
T58F4 001:142.491 JLINK_ReadReg(R0)
T58F4 001:142.496 - 0.007ms returns 0x00000000
T58F4 001:142.835 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:142.843   Data:  0C FE 10 EB 0E 00 43 FA 0C FE 41 EB 0E 01 CC F1 ...
T58F4 001:142.856   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:150.199 - 7.377ms returns 0x29C
T58F4 001:150.219 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:150.224   Data:  C3 F1 9D 03 0B D0 51 EA C3 51 01 EB 12 20 53 06 ...
T58F4 001:150.237   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:154.381 - 4.170ms returns 0x164
T58F4 001:154.398 JLINK_HasError()
T58F4 001:154.406 JLINK_WriteReg(R0, 0x08002800)
T58F4 001:154.414 - 0.010ms returns 0
T58F4 001:154.420 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:154.425 - 0.007ms returns 0
T58F4 001:154.431 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:154.436 - 0.007ms returns 0
T58F4 001:154.442 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:154.447 - 0.007ms returns 0
T58F4 001:154.453 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:154.457 - 0.007ms returns 0
T58F4 001:154.463 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:154.468 - 0.007ms returns 0
T58F4 001:154.474 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:154.478 - 0.007ms returns 0
T58F4 001:154.484 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:154.489 - 0.007ms returns 0
T58F4 001:154.495 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:154.500 - 0.007ms returns 0
T58F4 001:154.505 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:154.510 - 0.007ms returns 0
T58F4 001:154.516 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:154.521 - 0.007ms returns 0
T58F4 001:154.526 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:154.531 - 0.007ms returns 0
T58F4 001:154.537 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:154.542 - 0.007ms returns 0
T58F4 001:154.548 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:154.553 - 0.007ms returns 0
T58F4 001:154.559 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:154.564 - 0.007ms returns 0
T58F4 001:154.569 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:154.575 - 0.007ms returns 0
T58F4 001:154.580 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:154.585 - 0.007ms returns 0
T58F4 001:154.591 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:154.596 - 0.007ms returns 0
T58F4 001:154.601 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:154.607 - 0.007ms returns 0
T58F4 001:154.612 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:154.617 - 0.007ms returns 0
T58F4 001:154.623 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:154.628 - 0.008ms returns 0x00000023
T58F4 001:154.635 JLINK_Go()
T58F4 001:154.645   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:157.691 - 3.065ms
T58F4 001:157.713 JLINK_IsHalted()
T58F4 001:158.043 - 0.332ms returns FALSE
T58F4 001:158.054 JLINK_HasError()
T58F4 001:159.750 JLINK_IsHalted()
T58F4 001:160.110 - 0.368ms returns FALSE
T58F4 001:160.124 JLINK_HasError()
T58F4 001:161.917 JLINK_IsHalted()
T58F4 001:162.277 - 0.367ms returns FALSE
T58F4 001:162.290 JLINK_HasError()
T58F4 001:163.910 JLINK_IsHalted()
T58F4 001:164.277 - 0.370ms returns FALSE
T58F4 001:164.303 JLINK_HasError()
T58F4 001:165.367 JLINK_IsHalted()
T58F4 001:165.738 - 0.374ms returns FALSE
T58F4 001:165.745 JLINK_HasError()
T58F4 001:167.340 JLINK_IsHalted()
T58F4 001:167.736 - 0.406ms returns FALSE
T58F4 001:167.753 JLINK_HasError()
T58F4 001:169.345 JLINK_IsHalted()
T58F4 001:169.699 - 0.356ms returns FALSE
T58F4 001:169.707 JLINK_HasError()
T58F4 001:171.339 JLINK_IsHalted()
T58F4 001:171.680 - 0.343ms returns FALSE
T58F4 001:171.685 JLINK_HasError()
T58F4 001:173.356 JLINK_IsHalted()
T58F4 001:173.799 - 0.454ms returns FALSE
T58F4 001:173.817 JLINK_HasError()
T58F4 001:174.889 JLINK_IsHalted()
T58F4 001:175.249 - 0.367ms returns FALSE
T58F4 001:175.260 JLINK_HasError()
T58F4 001:176.899 JLINK_IsHalted()
T58F4 001:177.286 - 0.392ms returns FALSE
T58F4 001:177.297 JLINK_HasError()
T58F4 001:178.890 JLINK_IsHalted()
T58F4 001:179.267 - 0.380ms returns FALSE
T58F4 001:179.275 JLINK_HasError()
T58F4 001:180.891 JLINK_IsHalted()
T58F4 001:181.276 - 0.389ms returns FALSE
T58F4 001:181.285 JLINK_HasError()
T58F4 001:183.202 JLINK_IsHalted()
T58F4 001:183.624 - 0.425ms returns FALSE
T58F4 001:183.631 JLINK_HasError()
T58F4 001:185.203 JLINK_IsHalted()
T58F4 001:185.530 - 0.330ms returns FALSE
T58F4 001:185.537 JLINK_HasError()
T58F4 001:187.264 JLINK_IsHalted()
T58F4 001:187.624 - 0.362ms returns FALSE
T58F4 001:187.630 JLINK_HasError()
T58F4 001:189.261 JLINK_IsHalted()
T58F4 001:192.086   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:192.489 - 3.238ms returns TRUE
T58F4 001:192.506 JLINK_ReadReg(R15 (PC))
T58F4 001:192.516 - 0.012ms returns 0x20000000
T58F4 001:192.522 JLINK_ClrBPEx(BPHandle = 0x00000023)
T58F4 001:192.528 - 0.008ms returns 0x00
T58F4 001:192.534 JLINK_ReadReg(R0)
T58F4 001:192.539 - 0.007ms returns 0x00000000
T58F4 001:192.582 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:192.588   Data:  5F EA 41 0C 5F EA 1C 6C 08 D0 80 EA 01 00 00 F0 ...
T58F4 001:192.601   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:199.924 - 7.353ms returns 0x29C
T58F4 001:199.943 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:199.948   Data:  04 18 E0 00 00 00 00 40 20 18 07 00 00 00 40 40 ...
T58F4 001:199.962   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:204.117 - 4.184ms returns 0x164
T58F4 001:204.134 JLINK_HasError()
T58F4 001:204.141 JLINK_WriteReg(R0, 0x08002C00)
T58F4 001:204.150 - 0.010ms returns 0
T58F4 001:204.156 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:204.161 - 0.007ms returns 0
T58F4 001:204.167 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:204.172 - 0.007ms returns 0
T58F4 001:204.177 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:204.182 - 0.007ms returns 0
T58F4 001:204.188 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:204.193 - 0.007ms returns 0
T58F4 001:204.199 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:204.204 - 0.007ms returns 0
T58F4 001:204.210 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:204.215 - 0.007ms returns 0
T58F4 001:204.220 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:204.225 - 0.007ms returns 0
T58F4 001:204.231 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:204.236 - 0.007ms returns 0
T58F4 001:204.242 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:204.247 - 0.007ms returns 0
T58F4 001:204.252 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:204.257 - 0.007ms returns 0
T58F4 001:204.263 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:204.268 - 0.007ms returns 0
T58F4 001:204.273 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:204.278 - 0.007ms returns 0
T58F4 001:204.284 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:204.289 - 0.007ms returns 0
T58F4 001:204.295 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:204.300 - 0.007ms returns 0
T58F4 001:204.306 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:204.311 - 0.007ms returns 0
T58F4 001:204.316 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:204.321 - 0.007ms returns 0
T58F4 001:204.327 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:204.339 - 0.014ms returns 0
T58F4 001:204.345 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:204.350 - 0.007ms returns 0
T58F4 001:204.356 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:204.361 - 0.007ms returns 0
T58F4 001:204.367 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:204.373 - 0.008ms returns 0x00000024
T58F4 001:204.379 JLINK_Go()
T58F4 001:204.389   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:207.440 - 3.069ms
T58F4 001:207.454 JLINK_IsHalted()
T58F4 001:207.788 - 0.337ms returns FALSE
T58F4 001:207.795 JLINK_HasError()
T58F4 001:209.248 JLINK_IsHalted()
T58F4 001:209.626 - 0.381ms returns FALSE
T58F4 001:209.633 JLINK_HasError()
T58F4 001:211.230 JLINK_IsHalted()
T58F4 001:211.628 - 0.401ms returns FALSE
T58F4 001:211.635 JLINK_HasError()
T58F4 001:213.226 JLINK_IsHalted()
T58F4 001:213.626 - 0.402ms returns FALSE
T58F4 001:213.633 JLINK_HasError()
T58F4 001:215.310 JLINK_IsHalted()
T58F4 001:215.663 - 0.359ms returns FALSE
T58F4 001:215.676 JLINK_HasError()
T58F4 001:217.306 JLINK_IsHalted()
T58F4 001:217.658 - 0.358ms returns FALSE
T58F4 001:217.670 JLINK_HasError()
T58F4 001:219.304 JLINK_IsHalted()
T58F4 001:219.672 - 0.370ms returns FALSE
T58F4 001:219.678 JLINK_HasError()
T58F4 001:221.315 JLINK_IsHalted()
T58F4 001:221.671 - 0.359ms returns FALSE
T58F4 001:221.677 JLINK_HasError()
T58F4 001:223.306 JLINK_IsHalted()
T58F4 001:223.659 - 0.359ms returns FALSE
T58F4 001:223.672 JLINK_HasError()
T58F4 001:225.304 JLINK_IsHalted()
T58F4 001:225.690 - 0.388ms returns FALSE
T58F4 001:225.697 JLINK_HasError()
T58F4 001:227.304 JLINK_IsHalted()
T58F4 001:227.637 - 0.334ms returns FALSE
T58F4 001:227.643 JLINK_HasError()
T58F4 001:229.310 JLINK_IsHalted()
T58F4 001:229.668 - 0.361ms returns FALSE
T58F4 001:229.675 JLINK_HasError()
T58F4 001:231.311 JLINK_IsHalted()
T58F4 001:231.690 - 0.383ms returns FALSE
T58F4 001:231.698 JLINK_HasError()
T58F4 001:233.331 JLINK_IsHalted()
T58F4 001:233.693 - 0.365ms returns FALSE
T58F4 001:233.700 JLINK_HasError()
T58F4 001:235.695 JLINK_IsHalted()
T58F4 001:236.060 - 0.372ms returns FALSE
T58F4 001:236.072 JLINK_HasError()
T58F4 001:237.691 JLINK_IsHalted()
T58F4 001:238.026 - 0.337ms returns FALSE
T58F4 001:238.032 JLINK_HasError()
T58F4 001:240.026 JLINK_IsHalted()
T58F4 001:242.777   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:243.145 - 3.121ms returns TRUE
T58F4 001:243.151 JLINK_ReadReg(R15 (PC))
T58F4 001:243.157 - 0.007ms returns 0x20000000
T58F4 001:243.162 JLINK_ClrBPEx(BPHandle = 0x00000024)
T58F4 001:243.166 - 0.005ms returns 0x00
T58F4 001:243.170 JLINK_ReadReg(R0)
T58F4 001:243.174 - 0.005ms returns 0x00000000
T58F4 001:243.200 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:243.204   Data:  F0 00 00 00 00 30 36 01 00 00 C0 30 C8 28 E8 10 ...
T58F4 001:243.213   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:250.616 - 7.425ms returns 0x29C
T58F4 001:250.632 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:250.637   Data:  98 98 00 00 00 00 00 20 20 3F 20 20 00 00 00 00 ...
T58F4 001:250.649   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:254.774 - 4.151ms returns 0x164
T58F4 001:254.790 JLINK_HasError()
T58F4 001:254.797 JLINK_WriteReg(R0, 0x08003000)
T58F4 001:254.804 - 0.010ms returns 0
T58F4 001:254.810 JLINK_WriteReg(R1, 0x00000400)
T58F4 001:254.816 - 0.007ms returns 0
T58F4 001:254.821 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:254.826 - 0.007ms returns 0
T58F4 001:254.832 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:254.837 - 0.007ms returns 0
T58F4 001:254.843 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:254.847 - 0.007ms returns 0
T58F4 001:254.853 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:254.858 - 0.007ms returns 0
T58F4 001:254.864 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:254.869 - 0.007ms returns 0
T58F4 001:254.874 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:254.879 - 0.007ms returns 0
T58F4 001:254.885 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:254.890 - 0.007ms returns 0
T58F4 001:254.895 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:254.905 - 0.013ms returns 0
T58F4 001:254.912 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:254.917 - 0.007ms returns 0
T58F4 001:254.923 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:254.928 - 0.007ms returns 0
T58F4 001:254.934 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:254.938 - 0.007ms returns 0
T58F4 001:254.944 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:254.950 - 0.007ms returns 0
T58F4 001:254.955 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:254.960 - 0.007ms returns 0
T58F4 001:254.966 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:254.971 - 0.007ms returns 0
T58F4 001:254.976 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:254.982 - 0.007ms returns 0
T58F4 001:254.987 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:254.992 - 0.007ms returns 0
T58F4 001:254.998 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:255.003 - 0.007ms returns 0
T58F4 001:255.009 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:255.014 - 0.007ms returns 0
T58F4 001:255.019 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:255.025 - 0.008ms returns 0x00000025
T58F4 001:255.031 JLINK_Go()
T58F4 001:255.040   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:258.028 - 3.000ms
T58F4 001:258.036 JLINK_IsHalted()
T58F4 001:258.362 - 0.329ms returns FALSE
T58F4 001:258.369 JLINK_HasError()
T58F4 001:260.121 JLINK_IsHalted()
T58F4 001:260.489 - 0.370ms returns FALSE
T58F4 001:260.495 JLINK_HasError()
T58F4 001:262.142 JLINK_IsHalted()
T58F4 001:262.476 - 0.340ms returns FALSE
T58F4 001:262.488 JLINK_HasError()
T58F4 001:264.142 JLINK_IsHalted()
T58F4 001:264.477 - 0.338ms returns FALSE
T58F4 001:264.484 JLINK_HasError()
T58F4 001:266.147 JLINK_IsHalted()
T58F4 001:266.521 - 0.376ms returns FALSE
T58F4 001:266.529 JLINK_HasError()
T58F4 001:268.147 JLINK_IsHalted()
T58F4 001:268.496 - 0.351ms returns FALSE
T58F4 001:268.502 JLINK_HasError()
T58F4 001:270.142 JLINK_IsHalted()
T58F4 001:270.474 - 0.335ms returns FALSE
T58F4 001:270.481 JLINK_HasError()
T58F4 001:272.141 JLINK_IsHalted()
T58F4 001:272.509 - 0.372ms returns FALSE
T58F4 001:272.519 JLINK_HasError()
T58F4 001:274.142 JLINK_IsHalted()
T58F4 001:274.506 - 0.367ms returns FALSE
T58F4 001:274.513 JLINK_HasError()
T58F4 001:276.505 JLINK_IsHalted()
T58F4 001:276.929 - 0.434ms returns FALSE
T58F4 001:276.945 JLINK_HasError()
T58F4 001:278.512 JLINK_IsHalted()
T58F4 001:278.889 - 0.380ms returns FALSE
T58F4 001:278.897 JLINK_HasError()
T58F4 001:280.511 JLINK_IsHalted()
T58F4 001:280.888 - 0.380ms returns FALSE
T58F4 001:280.895 JLINK_HasError()
T58F4 001:282.534 JLINK_IsHalted()
T58F4 001:282.911 - 0.384ms returns FALSE
T58F4 001:282.925 JLINK_HasError()
T58F4 001:284.553 JLINK_IsHalted()
T58F4 001:284.897 - 0.346ms returns FALSE
T58F4 001:284.903 JLINK_HasError()
T58F4 001:286.510 JLINK_IsHalted()
T58F4 001:286.890 - 0.382ms returns FALSE
T58F4 001:286.897 JLINK_HasError()
T58F4 001:288.510 JLINK_IsHalted()
T58F4 001:288.849 - 0.341ms returns FALSE
T58F4 001:288.855 JLINK_HasError()
T58F4 001:290.513 JLINK_IsHalted()
T58F4 001:293.301   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:293.657 - 3.146ms returns TRUE
T58F4 001:293.665 JLINK_ReadReg(R15 (PC))
T58F4 001:293.671 - 0.008ms returns 0x20000000
T58F4 001:293.677 JLINK_ClrBPEx(BPHandle = 0x00000025)
T58F4 001:293.682 - 0.007ms returns 0x00
T58F4 001:293.688 JLINK_ReadReg(R0)
T58F4 001:293.693 - 0.007ms returns 0x00000000
T58F4 001:293.737 JLINK_WriteMem(0x20000164, 0x29C Bytes, ...)
T58F4 001:293.743   Data:  00 00 00 20 28 00 00 00 28 01 00 08 44 34 00 08 ...
T58F4 001:293.753   CPU_WriteMem(668 bytes @ 0x20000164)
T58F4 001:301.129 - 7.403ms returns 0x29C
T58F4 001:301.147 JLINK_WriteMem(0x20000400, 0x164 Bytes, ...)
T58F4 001:301.153   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T58F4 001:301.171   CPU_WriteMem(356 bytes @ 0x20000400)
T58F4 001:305.307 - 4.167ms returns 0x164
T58F4 001:305.321 JLINK_HasError()
T58F4 001:305.328 JLINK_WriteReg(R0, 0x08003400)
T58F4 001:305.335 - 0.010ms returns 0
T58F4 001:305.341 JLINK_WriteReg(R1, 0x00000044)
T58F4 001:305.350 - 0.012ms returns 0
T58F4 001:305.357 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:305.362 - 0.007ms returns 0
T58F4 001:305.368 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:305.373 - 0.007ms returns 0
T58F4 001:305.378 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:305.383 - 0.007ms returns 0
T58F4 001:305.389 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:305.394 - 0.007ms returns 0
T58F4 001:305.400 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:305.405 - 0.007ms returns 0
T58F4 001:305.410 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:305.415 - 0.007ms returns 0
T58F4 001:305.421 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:305.426 - 0.007ms returns 0
T58F4 001:305.432 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:305.436 - 0.007ms returns 0
T58F4 001:305.442 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:305.447 - 0.007ms returns 0
T58F4 001:305.453 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:305.457 - 0.007ms returns 0
T58F4 001:305.463 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:305.468 - 0.007ms returns 0
T58F4 001:305.474 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:305.479 - 0.007ms returns 0
T58F4 001:305.485 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:305.490 - 0.007ms returns 0
T58F4 001:305.495 JLINK_WriteReg(R15 (PC), 0x200000F4)
T58F4 001:305.500 - 0.007ms returns 0
T58F4 001:305.506 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:305.511 - 0.007ms returns 0
T58F4 001:305.517 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:305.521 - 0.007ms returns 0
T58F4 001:305.527 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:305.532 - 0.007ms returns 0
T58F4 001:305.538 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:305.543 - 0.007ms returns 0
T58F4 001:305.549 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:305.554 - 0.007ms returns 0x00000026
T58F4 001:305.560 JLINK_Go()
T58F4 001:305.570   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:308.539 - 2.987ms
T58F4 001:308.553 JLINK_IsHalted()
T58F4 001:308.891 - 0.341ms returns FALSE
T58F4 001:308.898 JLINK_HasError()
T58F4 001:310.074 JLINK_IsHalted()
T58F4 001:310.437 - 0.365ms returns FALSE
T58F4 001:310.450 JLINK_HasError()
T58F4 001:312.070 JLINK_IsHalted()
T58F4 001:314.820   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:315.194 - 3.130ms returns TRUE
T58F4 001:315.207 JLINK_ReadReg(R15 (PC))
T58F4 001:315.214 - 0.010ms returns 0x20000000
T58F4 001:315.220 JLINK_ClrBPEx(BPHandle = 0x00000026)
T58F4 001:315.226 - 0.007ms returns 0x00
T58F4 001:315.231 JLINK_ReadReg(R0)
T58F4 001:315.236 - 0.007ms returns 0x00000000
T58F4 001:315.243 JLINK_HasError()
T58F4 001:315.249 JLINK_WriteReg(R0, 0x00000002)
T58F4 001:315.254 - 0.007ms returns 0
T58F4 001:315.260 JLINK_WriteReg(R1, 0x00000044)
T58F4 001:315.265 - 0.007ms returns 0
T58F4 001:315.271 JLINK_WriteReg(R2, 0x20000164)
T58F4 001:315.276 - 0.007ms returns 0
T58F4 001:315.281 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:315.286 - 0.007ms returns 0
T58F4 001:315.292 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:315.297 - 0.007ms returns 0
T58F4 001:315.302 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:315.307 - 0.007ms returns 0
T58F4 001:315.313 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:315.318 - 0.007ms returns 0
T58F4 001:315.323 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:315.328 - 0.007ms returns 0
T58F4 001:315.334 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:315.339 - 0.007ms returns 0
T58F4 001:315.344 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:315.349 - 0.007ms returns 0
T58F4 001:315.355 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:315.360 - 0.007ms returns 0
T58F4 001:315.365 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:315.370 - 0.007ms returns 0
T58F4 001:315.376 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:315.381 - 0.007ms returns 0
T58F4 001:315.387 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:315.392 - 0.007ms returns 0
T58F4 001:315.398 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:315.403 - 0.007ms returns 0
T58F4 001:315.408 JLINK_WriteReg(R15 (PC), 0x2000006A)
T58F4 001:315.413 - 0.007ms returns 0
T58F4 001:315.419 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:315.424 - 0.010ms returns 0
T58F4 001:315.434 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:315.439 - 0.007ms returns 0
T58F4 001:315.445 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:315.450 - 0.007ms returns 0
T58F4 001:315.455 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:315.460 - 0.007ms returns 0
T58F4 001:315.466 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:315.471 - 0.007ms returns 0x00000027
T58F4 001:315.477 JLINK_Go()
T58F4 001:315.485   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:318.451 - 2.977ms
T58F4 001:318.459 JLINK_IsHalted()
T58F4 001:321.219   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:321.637 - 3.181ms returns TRUE
T58F4 001:321.645 JLINK_ReadReg(R15 (PC))
T58F4 001:321.653 - 0.009ms returns 0x20000000
T58F4 001:321.659 JLINK_ClrBPEx(BPHandle = 0x00000027)
T58F4 001:321.664 - 0.007ms returns 0x00
T58F4 001:321.669 JLINK_ReadReg(R0)
T58F4 001:321.674 - 0.007ms returns 0x00000000
T58F4 001:372.886 JLINK_WriteMem(0x20000000, 0x164 Bytes, ...)
T58F4 001:372.898   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T58F4 001:372.918   CPU_WriteMem(356 bytes @ 0x20000000)
T58F4 001:377.065 - 4.195ms returns 0x164
T58F4 001:377.107 JLINK_HasError()
T58F4 001:377.114 JLINK_WriteReg(R0, 0x08000000)
T58F4 001:377.121 - 0.009ms returns 0
T58F4 001:377.126 JLINK_WriteReg(R1, 0x00B71B00)
T58F4 001:377.130 - 0.005ms returns 0
T58F4 001:377.134 JLINK_WriteReg(R2, 0x00000003)
T58F4 001:377.138 - 0.005ms returns 0
T58F4 001:377.142 JLINK_WriteReg(R3, 0x00000000)
T58F4 001:377.146 - 0.005ms returns 0
T58F4 001:377.150 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:377.153 - 0.005ms returns 0
T58F4 001:377.158 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:377.161 - 0.005ms returns 0
T58F4 001:377.166 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:377.169 - 0.005ms returns 0
T58F4 001:377.173 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:377.177 - 0.005ms returns 0
T58F4 001:377.181 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:377.185 - 0.005ms returns 0
T58F4 001:377.189 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:377.193 - 0.005ms returns 0
T58F4 001:377.197 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:377.200 - 0.005ms returns 0
T58F4 001:377.205 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:377.209 - 0.005ms returns 0
T58F4 001:377.213 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:377.216 - 0.005ms returns 0
T58F4 001:377.221 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:377.225 - 0.006ms returns 0
T58F4 001:377.229 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:377.233 - 0.005ms returns 0
T58F4 001:377.237 JLINK_WriteReg(R15 (PC), 0x20000038)
T58F4 001:377.241 - 0.005ms returns 0
T58F4 001:377.245 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:377.249 - 0.005ms returns 0
T58F4 001:377.253 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:377.257 - 0.005ms returns 0
T58F4 001:377.261 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:377.265 - 0.005ms returns 0
T58F4 001:377.269 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:377.272 - 0.005ms returns 0
T58F4 001:377.277 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:377.285   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:377.711 - 0.445ms returns 0x00000028
T58F4 001:377.729 JLINK_Go()
T58F4 001:377.738   CPU_WriteMem(2 bytes @ 0x20000000)
T58F4 001:378.176   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:381.444 - 3.728ms
T58F4 001:381.467 JLINK_IsHalted()
T58F4 001:384.358   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:384.743 - 3.278ms returns TRUE
T58F4 001:384.750 JLINK_ReadReg(R15 (PC))
T58F4 001:384.758 - 0.009ms returns 0x20000000
T58F4 001:384.764 JLINK_ClrBPEx(BPHandle = 0x00000028)
T58F4 001:384.769 - 0.007ms returns 0x00
T58F4 001:384.775 JLINK_ReadReg(R0)
T58F4 001:384.780 - 0.007ms returns 0x00000000
T58F4 001:384.786 JLINK_HasError()
T58F4 001:384.792 JLINK_WriteReg(R0, 0xFFFFFFFF)
T58F4 001:384.798 - 0.007ms returns 0
T58F4 001:384.803 JLINK_WriteReg(R1, 0x08000000)
T58F4 001:384.808 - 0.007ms returns 0
T58F4 001:384.814 JLINK_WriteReg(R2, 0x00003444)
T58F4 001:384.819 - 0.007ms returns 0
T58F4 001:384.824 JLINK_WriteReg(R3, 0x04C11DB7)
T58F4 001:384.833 - 0.013ms returns 0
T58F4 001:384.842 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:384.846 - 0.007ms returns 0
T58F4 001:384.852 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:384.857 - 0.007ms returns 0
T58F4 001:384.863 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:384.868 - 0.007ms returns 0
T58F4 001:384.873 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:384.878 - 0.007ms returns 0
T58F4 001:384.884 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:384.889 - 0.007ms returns 0
T58F4 001:384.895 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:384.899 - 0.007ms returns 0
T58F4 001:384.905 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:384.910 - 0.007ms returns 0
T58F4 001:384.916 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:384.920 - 0.007ms returns 0
T58F4 001:384.926 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:384.931 - 0.007ms returns 0
T58F4 001:384.937 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:384.942 - 0.007ms returns 0
T58F4 001:384.948 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:384.952 - 0.007ms returns 0
T58F4 001:384.958 JLINK_WriteReg(R15 (PC), 0x20000002)
T58F4 001:384.963 - 0.007ms returns 0
T58F4 001:384.968 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:384.973 - 0.007ms returns 0
T58F4 001:384.979 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:384.984 - 0.007ms returns 0
T58F4 001:384.989 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:384.994 - 0.007ms returns 0
T58F4 001:385.000 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:385.005 - 0.007ms returns 0
T58F4 001:385.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:385.016 - 0.008ms returns 0x00000029
T58F4 001:385.022 JLINK_Go()
T58F4 001:385.031   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:388.046 - 3.032ms
T58F4 001:388.068 JLINK_IsHalted()
T58F4 001:388.427 - 0.362ms returns FALSE
T58F4 001:388.438 JLINK_HasError()
T58F4 001:389.729 JLINK_IsHalted()
T58F4 001:390.092 - 0.364ms returns FALSE
T58F4 001:390.097 JLINK_HasError()
T58F4 001:391.730 JLINK_IsHalted()
T58F4 001:392.068 - 0.346ms returns FALSE
T58F4 001:392.082 JLINK_HasError()
T58F4 001:394.250 JLINK_IsHalted()
T58F4 001:394.641 - 0.396ms returns FALSE
T58F4 001:394.651 JLINK_HasError()
T58F4 001:396.642 JLINK_IsHalted()
T58F4 001:397.013 - 0.374ms returns FALSE
T58F4 001:397.021 JLINK_HasError()
T58F4 001:398.648 JLINK_IsHalted()
T58F4 001:399.005 - 0.364ms returns FALSE
T58F4 001:399.018 JLINK_HasError()
T58F4 001:400.727 JLINK_IsHalted()
T58F4 001:401.095 - 0.371ms returns FALSE
T58F4 001:401.102 JLINK_HasError()
T58F4 001:403.231 JLINK_IsHalted()
T58F4 001:403.641 - 0.413ms returns FALSE
T58F4 001:403.648 JLINK_HasError()
T58F4 001:405.234 JLINK_IsHalted()
T58F4 001:405.642 - 0.414ms returns FALSE
T58F4 001:405.654 JLINK_HasError()
T58F4 001:407.233 JLINK_IsHalted()
T58F4 001:407.639 - 0.409ms returns FALSE
T58F4 001:407.646 JLINK_HasError()
T58F4 001:409.241 JLINK_IsHalted()
T58F4 001:409.640 - 0.402ms returns FALSE
T58F4 001:409.648 JLINK_HasError()
T58F4 001:411.235 JLINK_IsHalted()
T58F4 001:411.639 - 0.406ms returns FALSE
T58F4 001:411.645 JLINK_HasError()
T58F4 001:413.234 JLINK_IsHalted()
T58F4 001:413.639 - 0.408ms returns FALSE
T58F4 001:413.652 JLINK_HasError()
T58F4 001:415.236 JLINK_IsHalted()
T58F4 001:415.641 - 0.408ms returns FALSE
T58F4 001:415.648 JLINK_HasError()
T58F4 001:417.644 JLINK_IsHalted()
T58F4 001:417.999 - 0.357ms returns FALSE
T58F4 001:418.007 JLINK_HasError()
T58F4 001:419.639 JLINK_IsHalted()
T58F4 001:420.015 - 0.379ms returns FALSE
T58F4 001:420.023 JLINK_HasError()
T58F4 001:421.740 JLINK_IsHalted()
T58F4 001:422.072 - 0.334ms returns FALSE
T58F4 001:422.079 JLINK_HasError()
T58F4 001:423.744 JLINK_IsHalted()
T58F4 001:424.119 - 0.381ms returns FALSE
T58F4 001:424.130 JLINK_HasError()
T58F4 001:425.740 JLINK_IsHalted()
T58F4 001:426.106 - 0.368ms returns FALSE
T58F4 001:426.111 JLINK_HasError()
T58F4 001:427.741 JLINK_IsHalted()
T58F4 001:428.073 - 0.334ms returns FALSE
T58F4 001:428.080 JLINK_HasError()
T58F4 001:429.743 JLINK_IsHalted()
T58F4 001:430.094 - 0.358ms returns FALSE
T58F4 001:430.106 JLINK_HasError()
T58F4 001:431.742 JLINK_IsHalted()
T58F4 001:432.107 - 0.367ms returns FALSE
T58F4 001:432.114 JLINK_HasError()
T58F4 001:433.742 JLINK_IsHalted()
T58F4 001:434.075 - 0.336ms returns FALSE
T58F4 001:434.082 JLINK_HasError()
T58F4 001:435.746 JLINK_IsHalted()
T58F4 001:436.092 - 0.348ms returns FALSE
T58F4 001:436.098 JLINK_HasError()
T58F4 001:438.092 JLINK_IsHalted()
T58F4 001:438.424 - 0.335ms returns FALSE
T58F4 001:438.431 JLINK_HasError()
T58F4 001:440.104 JLINK_IsHalted()
T58F4 001:440.483 - 0.382ms returns FALSE
T58F4 001:440.490 JLINK_HasError()
T58F4 001:442.100 JLINK_IsHalted()
T58F4 001:442.466 - 0.371ms returns FALSE
T58F4 001:442.475 JLINK_HasError()
T58F4 001:444.092 JLINK_IsHalted()
T58F4 001:444.431 - 0.341ms returns FALSE
T58F4 001:444.438 JLINK_HasError()
T58F4 001:446.154 JLINK_IsHalted()
T58F4 001:446.558 - 0.416ms returns FALSE
T58F4 001:446.578 JLINK_HasError()
T58F4 001:448.161 JLINK_IsHalted()
T58F4 001:448.519 - 0.365ms returns FALSE
T58F4 001:448.532 JLINK_HasError()
T58F4 001:450.155 JLINK_IsHalted()
T58F4 001:450.513 - 0.362ms returns FALSE
T58F4 001:450.521 JLINK_HasError()
T58F4 001:452.156 JLINK_IsHalted()
T58F4 001:452.570 - 0.416ms returns FALSE
T58F4 001:452.576 JLINK_HasError()
T58F4 001:454.161 JLINK_IsHalted()
T58F4 001:454.542 - 0.390ms returns FALSE
T58F4 001:454.557 JLINK_HasError()
T58F4 001:456.155 JLINK_IsHalted()
T58F4 001:456.493 - 0.344ms returns FALSE
T58F4 001:456.503 JLINK_HasError()
T58F4 001:458.156 JLINK_IsHalted()
T58F4 001:458.523 - 0.370ms returns FALSE
T58F4 001:458.530 JLINK_HasError()
T58F4 001:460.165 JLINK_IsHalted()
T58F4 001:460.527 - 0.365ms returns FALSE
T58F4 001:460.533 JLINK_HasError()
T58F4 001:462.526 JLINK_IsHalted()
T58F4 001:462.860 - 0.337ms returns FALSE
T58F4 001:462.867 JLINK_HasError()
T58F4 001:464.529 JLINK_IsHalted()
T58F4 001:464.901 - 0.380ms returns FALSE
T58F4 001:464.924 JLINK_HasError()
T58F4 001:466.629 JLINK_IsHalted()
T58F4 001:466.978 - 0.356ms returns FALSE
T58F4 001:466.991 JLINK_HasError()
T58F4 001:468.626 JLINK_IsHalted()
T58F4 001:468.987 - 0.363ms returns FALSE
T58F4 001:468.994 JLINK_HasError()
T58F4 001:470.631 JLINK_IsHalted()
T58F4 001:470.977 - 0.353ms returns FALSE
T58F4 001:470.991 JLINK_HasError()
T58F4 001:472.629 JLINK_IsHalted()
T58F4 001:472.979 - 0.356ms returns FALSE
T58F4 001:472.991 JLINK_HasError()
T58F4 001:474.627 JLINK_IsHalted()
T58F4 001:474.967 - 0.342ms returns FALSE
T58F4 001:474.974 JLINK_HasError()
T58F4 001:476.627 JLINK_IsHalted()
T58F4 001:476.966 - 0.347ms returns FALSE
T58F4 001:476.980 JLINK_HasError()
T58F4 001:478.628 JLINK_IsHalted()
T58F4 001:478.979 - 0.353ms returns FALSE
T58F4 001:478.984 JLINK_HasError()
T58F4 001:480.633 JLINK_IsHalted()
T58F4 001:480.969 - 0.339ms returns FALSE
T58F4 001:480.976 JLINK_HasError()
T58F4 001:482.974 JLINK_IsHalted()
T58F4 001:483.351 - 0.384ms returns FALSE
T58F4 001:483.363 JLINK_HasError()
T58F4 001:484.968 JLINK_IsHalted()
T58F4 001:485.339 - 0.379ms returns FALSE
T58F4 001:485.353 JLINK_HasError()
T58F4 001:486.967 JLINK_IsHalted()
T58F4 001:487.307 - 0.344ms returns FALSE
T58F4 001:487.316 JLINK_HasError()
T58F4 001:488.980 JLINK_IsHalted()
T58F4 001:489.355 - 0.385ms returns FALSE
T58F4 001:489.371 JLINK_HasError()
T58F4 001:490.975 JLINK_IsHalted()
T58F4 001:491.344 - 0.373ms returns FALSE
T58F4 001:491.353 JLINK_HasError()
T58F4 001:492.974 JLINK_IsHalted()
T58F4 001:493.347 - 0.376ms returns FALSE
T58F4 001:493.355 JLINK_HasError()
T58F4 001:494.498 JLINK_IsHalted()
T58F4 001:494.906 - 0.412ms returns FALSE
T58F4 001:494.914 JLINK_HasError()
T58F4 001:496.482 JLINK_IsHalted()
T58F4 001:496.859 - 0.382ms returns FALSE
T58F4 001:496.869 JLINK_HasError()
T58F4 001:498.478 JLINK_IsHalted()
T58F4 001:498.817 - 0.342ms returns FALSE
T58F4 001:498.824 JLINK_HasError()
T58F4 001:500.494 JLINK_IsHalted()
T58F4 001:500.857 - 0.371ms returns FALSE
T58F4 001:500.871 JLINK_HasError()
T58F4 001:502.480 JLINK_IsHalted()
T58F4 001:502.864 - 0.389ms returns FALSE
T58F4 001:502.874 JLINK_HasError()
T58F4 001:504.482 JLINK_IsHalted()
T58F4 001:504.841 - 0.362ms returns FALSE
T58F4 001:504.848 JLINK_HasError()
T58F4 001:506.508 JLINK_IsHalted()
T58F4 001:506.867 - 0.366ms returns FALSE
T58F4 001:506.880 JLINK_HasError()
T58F4 001:508.477 JLINK_IsHalted()
T58F4 001:508.850 - 0.377ms returns FALSE
T58F4 001:508.859 JLINK_HasError()
T58F4 001:510.849 JLINK_IsHalted()
T58F4 001:511.241 - 0.394ms returns FALSE
T58F4 001:511.248 JLINK_HasError()
T58F4 001:513.247 JLINK_IsHalted()
T58F4 001:513.649 - 0.410ms returns FALSE
T58F4 001:513.664 JLINK_HasError()
T58F4 001:515.268 JLINK_IsHalted()
T58F4 001:515.648 - 0.383ms returns FALSE
T58F4 001:515.656 JLINK_HasError()
T58F4 001:517.273 JLINK_IsHalted()
T58F4 001:517.655 - 0.393ms returns FALSE
T58F4 001:517.674 JLINK_HasError()
T58F4 001:519.275 JLINK_IsHalted()
T58F4 001:519.670 - 0.405ms returns FALSE
T58F4 001:519.686 JLINK_HasError()
T58F4 001:521.241 JLINK_IsHalted()
T58F4 001:521.650 - 0.415ms returns FALSE
T58F4 001:521.662 JLINK_HasError()
T58F4 001:523.240 JLINK_IsHalted()
T58F4 001:523.646 - 0.409ms returns FALSE
T58F4 001:523.653 JLINK_HasError()
T58F4 001:525.239 JLINK_IsHalted()
T58F4 001:525.649 - 0.417ms returns FALSE
T58F4 001:525.663 JLINK_HasError()
T58F4 001:527.242 JLINK_IsHalted()
T58F4 001:527.649 - 0.414ms returns FALSE
T58F4 001:527.662 JLINK_HasError()
T58F4 001:529.242 JLINK_IsHalted()
T58F4 001:529.649 - 0.410ms returns FALSE
T58F4 001:529.656 JLINK_HasError()
T58F4 001:531.653 JLINK_IsHalted()
T58F4 001:532.028 - 0.382ms returns FALSE
T58F4 001:532.041 JLINK_HasError()
T58F4 001:533.767 JLINK_IsHalted()
T58F4 001:534.141 - 0.376ms returns FALSE
T58F4 001:534.148 JLINK_HasError()
T58F4 001:535.767 JLINK_IsHalted()
T58F4 001:538.532   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:538.933 - 3.169ms returns TRUE
T58F4 001:538.941 JLINK_ReadReg(R15 (PC))
T58F4 001:538.950 - 0.011ms returns 0x20000000
T58F4 001:538.956 JLINK_ClrBPEx(BPHandle = 0x00000029)
T58F4 001:538.962 - 0.007ms returns 0x00
T58F4 001:538.968 JLINK_ReadReg(R0)
T58F4 001:538.973 - 0.007ms returns 0xD13CF6C3
T58F4 001:539.401 JLINK_HasError()
T58F4 001:539.409 JLINK_WriteReg(R0, 0x00000003)
T58F4 001:539.414 - 0.008ms returns 0
T58F4 001:539.420 JLINK_WriteReg(R1, 0x08000000)
T58F4 001:539.425 - 0.007ms returns 0
T58F4 001:539.431 JLINK_WriteReg(R2, 0x00003444)
T58F4 001:539.436 - 0.007ms returns 0
T58F4 001:539.442 JLINK_WriteReg(R3, 0x04C11DB7)
T58F4 001:539.447 - 0.007ms returns 0
T58F4 001:539.452 JLINK_WriteReg(R4, 0x00000000)
T58F4 001:539.457 - 0.007ms returns 0
T58F4 001:539.463 JLINK_WriteReg(R5, 0x00000000)
T58F4 001:539.468 - 0.007ms returns 0
T58F4 001:539.473 JLINK_WriteReg(R6, 0x00000000)
T58F4 001:539.478 - 0.007ms returns 0
T58F4 001:539.484 JLINK_WriteReg(R7, 0x00000000)
T58F4 001:539.489 - 0.007ms returns 0
T58F4 001:539.495 JLINK_WriteReg(R8, 0x00000000)
T58F4 001:539.499 - 0.007ms returns 0
T58F4 001:539.505 JLINK_WriteReg(R9, 0x20000160)
T58F4 001:539.510 - 0.007ms returns 0
T58F4 001:539.516 JLINK_WriteReg(R10, 0x00000000)
T58F4 001:539.521 - 0.007ms returns 0
T58F4 001:539.526 JLINK_WriteReg(R11, 0x00000000)
T58F4 001:539.531 - 0.007ms returns 0
T58F4 001:539.537 JLINK_WriteReg(R12, 0x00000000)
T58F4 001:539.542 - 0.007ms returns 0
T58F4 001:539.547 JLINK_WriteReg(R13 (SP), 0x20001000)
T58F4 001:539.553 - 0.007ms returns 0
T58F4 001:539.559 JLINK_WriteReg(R14, 0x20000001)
T58F4 001:539.564 - 0.007ms returns 0
T58F4 001:539.569 JLINK_WriteReg(R15 (PC), 0x2000006A)
T58F4 001:539.574 - 0.007ms returns 0
T58F4 001:539.580 JLINK_WriteReg(XPSR, 0x01000000)
T58F4 001:539.585 - 0.007ms returns 0
T58F4 001:539.591 JLINK_WriteReg(MSP, 0x20001000)
T58F4 001:539.595 - 0.007ms returns 0
T58F4 001:539.601 JLINK_WriteReg(PSP, 0x20001000)
T58F4 001:539.606 - 0.007ms returns 0
T58F4 001:539.612 JLINK_WriteReg(CFBP, 0x00000000)
T58F4 001:539.617 - 0.007ms returns 0
T58F4 001:539.623 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T58F4 001:539.635 - 0.014ms returns 0x0000002A
T58F4 001:539.641 JLINK_Go()
T58F4 001:539.651   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:542.650 - 3.014ms
T58F4 001:542.666 JLINK_IsHalted()
T58F4 001:545.431   CPU_ReadMem(2 bytes @ 0x20000000)
T58F4 001:545.804 - 3.143ms returns TRUE
T58F4 001:545.820 JLINK_ReadReg(R15 (PC))
T58F4 001:545.826 - 0.008ms returns 0x20000000
T58F4 001:545.833 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T58F4 001:545.837 - 0.006ms returns 0x00
T58F4 001:545.843 JLINK_ReadReg(R0)
T58F4 001:545.847 - 0.005ms returns 0x00000000
T58F4 001:596.938 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T58F4 001:596.960   Data:  FE E7
T58F4 001:596.984   CPU_WriteMem(2 bytes @ 0x20000000)
T58F4 001:597.427 - 0.494ms returns 0x2
T58F4 001:597.438 JLINK_HasError()
T58F4 001:606.162 JLINK_Close()
T58F4 001:606.400   CPU_ReadMem(4 bytes @ 0xE0001000)
T58F4 001:613.542 - 7.389ms
T58F4 001:613.554   
T58F4 001:613.557   Closed
