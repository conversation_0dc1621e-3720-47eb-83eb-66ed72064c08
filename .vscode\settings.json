{"C_Cpp.default.includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Start", "${workspaceFolder}/Library", "${workspaceFolder}/System", "${workspaceFolder}/User"], "C_Cpp.default.defines": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD", "__GNUC__", "STM32F10X", "ARM_MATH_CM3"], "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "gcc-arm", "files.associations": {"*.h": "c", "*.c": "c"}, "C_Cpp.errorSquiggles": "disabled", "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.intelliSenseEngine": "default", "files.encoding": "gb2312"}