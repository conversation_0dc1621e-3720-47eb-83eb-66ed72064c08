Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart2.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart3.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    gray_go.o(i.Car_TurnAngle) refers to gray_go.o(i.L_Angle_Count) for L_Angle_Count
    gray_go.o(i.Car_TurnAngle) refers to gray_go.o(i.R_Angle_Count) for R_Angle_Count
    gray_go.o(i.Car_TurnAngle) refers to gray_go.o(.data) for NOW_ANGLE
    gray_go.o(i.Car_TurnAngle) refers to tb6612.o(i.Car_Retreat) for Car_Retreat
    gray_go.o(i.Car_TurnAngle) refers to tb6612.o(i.Car_Letreat) for Car_Letreat
    gray_go.o(i.L_Angle_Count) refers to delay.o(i.delay_ms) for delay_ms
    gray_go.o(i.L_Angle_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    gray_go.o(i.L_Angle_Count) refers to grayscale.o(i.ALL_Count) for ALL_Count
    gray_go.o(i.L_Angle_Count) refers to gray_go.o(i.anti_SearchRun) for anti_SearchRun
    gray_go.o(i.R_Angle_Count) refers to delay.o(i.delay_ms) for delay_ms
    gray_go.o(i.R_Angle_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    gray_go.o(i.R_Angle_Count) refers to grayscale.o(i.ALL_Count) for ALL_Count
    gray_go.o(i.R_Angle_Count) refers to gray_go.o(i.anti_SearchRun) for anti_SearchRun
    gray_go.o(i.SearchRun) refers to tb6612.o(i.Set_Motor) for Set_Motor
    gray_go.o(i.SearchRun) refers to delay.o(i.delay_ms) for delay_ms
    gray_go.o(i.anti_SearchRun) refers to tb6612.o(i.Set_Motor) for Set_Motor
    gray_go.o(i.anti_SearchRun) refers to delay.o(i.delay_ms) for delay_ms
    grayscale.o(i.ALL_Count) refers to grayscale.o(i.scan_Count_Flag) for scan_Count_Flag
    grayscale.o(i.ALL_Count) refers to delay.o(i.delay_ms) for delay_ms
    grayscale.o(i.ALL_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    grayscale.o(i.Grayscale_Init) refers to grayscale.o(i.HDIO_Init) for HDIO_Init
    grayscale.o(i.Grayscale_Init) refers to grayscale.o(i.eight_gray1) for eight_gray1
    grayscale.o(i.Grayscale_Init) refers to grayscale.o(i.eight_gray2) for eight_gray2
    grayscale.o(i.HDIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    grayscale.o(i.HDIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    grayscale.o(i.RL_Back_Count) refers to delay.o(i.delay_ms) for delay_ms
    grayscale.o(i.RL_Back_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    grayscale.o(i.RL_Centre_Count) refers to delay.o(i.delay_ms) for delay_ms
    grayscale.o(i.RL_Centre_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    grayscale.o(i.RL_Front_Count) refers to delay.o(i.delay_ms) for delay_ms
    grayscale.o(i.RL_Front_Count) refers to tb6612.o(i.Set_Motor) for Set_Motor
    grayscale.o(i.eight_gray1) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    grayscale.o(i.eight_gray1) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    grayscale.o(i.eight_gray2) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    grayscale.o(i.eight_gray2) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    grayscale.o(i.eight_gray2) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    tb6612.o(i.Car_Letreat) refers to tb6612.o(i.Set_Motor) for Set_Motor
    tb6612.o(i.Car_Retreat) refers to tb6612.o(i.Set_Motor) for Set_Motor
    tb6612.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tb6612.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tb6612.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tb6612.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tb6612.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    tb6612.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    tb6612.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tb6612.o(i.Get_Encoder_Count) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    tb6612.o(i.Get_Motor_Position) refers to tb6612.o(i.Get_Encoder_Count) for Get_Encoder_Count
    tb6612.o(i.Get_Motor_Position) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.TB6612_Init) for TB6612_Init
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.motor_mode) for motor_mode
    tb6612.o(i.MOTOR_Init) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.MOTOR_Init) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.Encoder_Init) for Encoder_Init
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.PID_Init) for PID_Init
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.Reset_Encoder) for Reset_Encoder
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.Turns_To_Pulses) for Turns_To_Pulses
    tb6612.o(i.MotorPID_GO) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.Get_Motor_Position) for Get_Motor_Position
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.PID_Calculate) for PID_Calculate
    tb6612.o(i.MotorPID_GO) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    tb6612.o(i.MotorPID_GO) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(i.Set_Motor_PWM) for Set_Motor_PWM
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(.data) for pid_initialized
    tb6612.o(i.MotorPID_GO) refers to tb6612.o(.bss) for motor_pid
    tb6612.o(i.Move_stop) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.Move_stop) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tb6612.o(i.PID_Calculate) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    tb6612.o(i.PID_Calculate) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    tb6612.o(i.PID_Calculate) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    tb6612.o(i.PID_Calculate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tb6612.o(i.PID_Init) refers to tb6612.o(.data) for pid_initialized
    tb6612.o(i.Reset_Encoder) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    tb6612.o(i.SetPWM) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.Set_Motor) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    tb6612.o(i.Set_Motor) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    tb6612.o(i.Set_Motor) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.Set_Motor) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tb6612.o(i.Set_Motor_PWM) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    tb6612.o(i.Set_Motor_PWM) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    tb6612.o(i.Set_Motor_PWM) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.Set_Motor_PWM) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tb6612.o(i.TB6612_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tb6612.o(i.TB6612_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tb6612.o(i.TB6612_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tb6612.o(i.TB6612_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    tb6612.o(i.TB6612_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tb6612.o(i.Turns_To_Pulses) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    tb6612.o(i.Turns_To_Pulses) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    tb6612.o(i.motor_mode) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tb6612.o(i.motor_mode) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tcs34725.o(i.Data_Processing) refers to tcs34725.o(.data) for RGB888
    tcs34725.o(i.TCS34725_CheckStatus) refers to tcs34725.o(i.TCS34725_ReadWord) for TCS34725_ReadWord
    tcs34725.o(i.TCS34725_GetRGB565) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    tcs34725.o(i.TCS34725_GetRGB565) refers to feqf.o(x$fpl$feqf) for __aeabi_cfcmpeq
    tcs34725.o(i.TCS34725_GetRGB565) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    tcs34725.o(i.TCS34725_GetRGB565) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tcs34725.o(i.TCS34725_GetRGB565) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    tcs34725.o(i.TCS34725_GetRGB888) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    tcs34725.o(i.TCS34725_GetRGB888) refers to feqf.o(x$fpl$feqf) for __aeabi_cfcmpeq
    tcs34725.o(i.TCS34725_GetRGB888) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    tcs34725.o(i.TCS34725_GetRGB888) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tcs34725.o(i.TCS34725_GetRGB888) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    tcs34725.o(i.TCS34725_Get_RGBData) refers to tcs34725.o(i.TCS34725_ReadWord) for TCS34725_ReadWord
    tcs34725.o(i.TCS34725_Get_RGBData) refers to tcs34725.o(.data) for C_Dat
    tcs34725.o(i.TCS34725_Init) refers to iic1.o(i.TCS34725_IIC_Init) for TCS34725_IIC_Init
    tcs34725.o(i.TCS34725_Init) refers to tcs34725.o(i.TCS34725_ReadWord) for TCS34725_ReadWord
    tcs34725.o(i.TCS34725_Init) refers to tcs34725.o(i.TCS34725_WriteByte) for TCS34725_WriteByte
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_start) for TCS34725_IIC_start
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_write_byte) for TCS34725_IIC_write_byte
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_Get_ack) for TCS34725_IIC_Get_ack
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_read_byte) for TCS34725_IIC_read_byte
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_NACK) for TCS34725_IIC_NACK
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_stop) for TCS34725_IIC_stop
    tcs34725.o(i.TCS34725_ReadWord) refers to iic1.o(i.TCS34725_IIC_ACK) for TCS34725_IIC_ACK
    tcs34725.o(i.TCS34725_WriteByte) refers to iic1.o(i.TCS34725_IIC_start) for TCS34725_IIC_start
    tcs34725.o(i.TCS34725_WriteByte) refers to iic1.o(i.TCS34725_IIC_write_byte) for TCS34725_IIC_write_byte
    tcs34725.o(i.TCS34725_WriteByte) refers to iic1.o(i.TCS34725_IIC_Get_ack) for TCS34725_IIC_Get_ack
    tcs34725.o(i.integrationTime) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    tcs34725.o(i.integrationTime) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    tcs34725.o(i.integrationTime) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    tcs34725.o(i.integrationTime) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    tcs34725.o(i.integrationTime) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    tcs34725.o(i.integrationTime) refers to tcs34725.o(i.TCS34725_WriteByte) for TCS34725_WriteByte
    tcs34725.o(i.integrationTime) refers to tcs34725.o(.data) for INTEGRATION_TIME_MS_MIN
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pca9685.o(i.PCA9685_Init) refers to iic3.o(i.IIC3_Init) for IIC3_Init
    pca9685.o(i.PCA9685_Init) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.PCA9685_Init) refers to delay.o(i.delay_ms) for delay_ms
    pca9685.o(i.PCA9685_Init) refers to pca9685.o(i.setPWMFreq) for setPWMFreq
    pca9685.o(i.PCA9685_read) refers to iic3.o(i.IIC3_Start) for IIC3_Start
    pca9685.o(i.PCA9685_read) refers to iic3.o(i.IIC3_Send_Byte) for IIC3_Send_Byte
    pca9685.o(i.PCA9685_read) refers to iic3.o(i.IIC3_Wait_Ack) for IIC3_Wait_Ack
    pca9685.o(i.PCA9685_read) refers to iic3.o(i.IIC3_Read_Byte) for IIC3_Read_Byte
    pca9685.o(i.PCA9685_read) refers to iic3.o(i.IIC3_Stop) for IIC3_Stop
    pca9685.o(i.PCA9685_write) refers to iic3.o(i.IIC3_Start) for IIC3_Start
    pca9685.o(i.PCA9685_write) refers to iic3.o(i.IIC3_Send_Byte) for IIC3_Send_Byte
    pca9685.o(i.PCA9685_write) refers to iic3.o(i.IIC3_Wait_Ack) for IIC3_Wait_Ack
    pca9685.o(i.PCA9685_write) refers to iic3.o(i.IIC3_Stop) for IIC3_Stop
    pca9685.o(i.calculate_PWM) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    pca9685.o(i.calculate_PWM) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pca9685.o(i.calculate_PWM) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pca9685.o(i.calculate_PWM) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pca9685.o(i.calculate_PWM) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    pca9685.o(i.crazyMe) refers to pca9685.o(i.calculate_PWM) for calculate_PWM
    pca9685.o(i.crazyMe) refers to pca9685.o(i.setPWM) for setPWM
    pca9685.o(i.crazyMe) refers to delay.o(i.delay_ms) for delay_ms
    pca9685.o(i.setPWM) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.setPWMFreq) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pca9685.o(i.setPWMFreq) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pca9685.o(i.setPWMFreq) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pca9685.o(i.setPWMFreq) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pca9685.o(i.setPWMFreq) refers to floor.o(i.floor) for floor
    pca9685.o(i.setPWMFreq) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    pca9685.o(i.setPWMFreq) refers to pca9685.o(i.PCA9685_read) for PCA9685_read
    pca9685.o(i.setPWMFreq) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.setPWMFreq) refers to delay.o(i.delay_ms) for delay_ms
    buzzer.o(i.BEED) refers to buzzer.o(i.Buzzer_ON) for Buzzer_ON
    buzzer.o(i.BEED) refers to delay.o(i.delay_ms) for delay_ms
    buzzer.o(i.BEED) refers to buzzer.o(i.Buzzer_OFF) for Buzzer_OFF
    buzzer.o(i.Buzzer_OFF) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    buzzer.o(i.Buzzer_ON) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    sys.o(i.MY_NVIC_Init) refers to sys.o(i.MY_NVIC_PriorityGroupConfig) for MY_NVIC_PriorityGroupConfig
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.MY_NVIC_PriorityGroupConfig) for MY_NVIC_PriorityGroupConfig
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.MY_NVIC_SetVectorTable) for MY_NVIC_SetVectorTable
    sys.o(i.Sys_Standby) refers to sys.o(.emb_text) for WFI_SET
    uart4.o(i.Uart4_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    uart4.o(i.Uart4_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    uart4.o(i.Uart4_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    uart4.o(i.Uart4_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    uart4.o(i.Uart4_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    uart4.o(i.Uart4_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    uart4.o(i.Uart4_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    uart4.o(i.mpu6050_send_data) refers to uart4.o(i.uart4_niming_report) for uart4_niming_report
    uart4.o(i.uart4_niming_report) refers to uart4.o(i.uart4_send_char) for uart4_send_char
    uart4.o(i.uart4_report_imu) refers to uart4.o(i.uart4_niming_report) for uart4_niming_report
    uart4.o(i.uart4_send_char) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    uart4.o(i.uart4_send_char) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    uart5.o(i.Uart5_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    uart5.o(i.Uart5_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    uart5.o(i.Uart5_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    uart5.o(i.Uart5_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    uart5.o(i.Uart5_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    uart5.o(i.Uart5_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    uart5.o(i.Uart5_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    uart5.o(i.Uart5_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    uart5.o(i.Uart5_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    uart5.o(i.Uart5_SendString) refers to uart5.o(i.Uart5_SendChar) for Uart5_SendChar
    usart1.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.data) for USART_RX_STA
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.bss) for USART_RX_BUF
    usart1.o(i.Usart1_Init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart1.o(i.Usart1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.Usart1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.Usart1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart1.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart1.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart1.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart2.o(i.Get_Usart2Date) refers to usart2.o(.data) for ReciveDatE
    usart2.o(i.Get_Usart2Flag) refers to usart2.o(.data) for ReciveFlag
    usart2.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart2.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart2.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart2.o(i.USART2_IRQHandler) refers to usart2.o(.data) for ReciveDatE
    usart2.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart2.o(i.USART2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart2.o(i.USART2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.USART2_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart2.o(i.USART2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart2.o(i.Usart2_Send) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart2.o(i.Usart2_Send) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_s) refers to delay.o(i.delay_ms) for delay_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(i.delay_ms) for delay_ms
    usart3.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart3.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart3.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart3.o(i.USART3_IRQHandler) refers to usart3.o(.data) for USART3_RxCounter
    usart3.o(i.USART3_IRQHandler) refers to usart3.o(.bss) for USART3_RxBuffer
    usart3.o(i.USART3_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart3.o(i.USART3_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart3.o(i.USART3_SendString) refers to usart3.o(i.USART3_SendChar) for USART3_SendChar
    usart3.o(i.USART_SendData_Buf) refers to usart3.o(i.USART3_SendChar) for USART3_SendChar
    usart3.o(i.Usart3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart3.o(i.Usart3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart3.o(i.Usart6_Init) refers to usart3.o(i.Usart3_Init) for Usart3_Init
    usart3.o(i.clearRxBuffer) refers to usart3.o(.data) for USART3_RxCounter
    usart3.o(i.isRxCompleted) refers to usart3.o(.data) for USART3_RxComplete
    tim3.o(i.PWM_SetCompare3_3) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tim3.o(i.PWM_SetCompare3_4) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim3.o(i.TIM3_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    tim4.o(i.PWM_SetCompare4_3) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    tim4.o(i.TIM4_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tim8.o(i.PWM_SetCompare8_4) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    tim8.o(i.TIM8_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    iic1.o(i.TCS34725_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    iic1.o(i.TCS34725_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    iic1.o(i.TCS34725_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_ACK) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_ACK) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_ACK) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_Delay) refers to delay.o(i.delay_us) for delay_us
    iic1.o(i.TCS34725_IIC_Get_ack) refers to iic1.o(i.SDA_Pin_IN) for SDA_Pin_IN
    iic1.o(i.TCS34725_IIC_Get_ack) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_Get_ack) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_Get_ack) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_Get_ack) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    iic1.o(i.TCS34725_IIC_Get_ack) refers to iic1.o(i.SDA_Pin_Output) for SDA_Pin_Output
    iic1.o(i.TCS34725_IIC_Init) refers to iic1.o(i.TCS34725_GPIO_Init) for TCS34725_GPIO_Init
    iic1.o(i.TCS34725_IIC_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_Init) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_NACK) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_NACK) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_NACK) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_read_byte) refers to iic1.o(i.SDA_Pin_IN) for SDA_Pin_IN
    iic1.o(i.TCS34725_IIC_read_byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_read_byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_read_byte) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_read_byte) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    iic1.o(i.TCS34725_IIC_read_byte) refers to iic1.o(i.SDA_Pin_Output) for SDA_Pin_Output
    iic1.o(i.TCS34725_IIC_start) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_start) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_start) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_stop) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_stop) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic1.o(i.TCS34725_IIC_write_byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    iic1.o(i.TCS34725_IIC_write_byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    iic1.o(i.TCS34725_IIC_write_byte) refers to iic1.o(i.TCS34725_IIC_Delay) for TCS34725_IIC_Delay
    iic2.o(i.IIC2_Ack) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    iic2.o(i.IIC2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    iic2.o(i.IIC2_NAck) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Read_Byte) refers to iic2.o(i.IIC2_NAck) for IIC2_NAck
    iic2.o(i.IIC2_Read_Byte) refers to iic2.o(i.IIC2_Ack) for IIC2_Ack
    iic2.o(i.IIC2_Read_One_Byte) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    iic2.o(i.IIC2_Read_One_Byte) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    iic2.o(i.IIC2_Read_One_Byte) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    iic2.o(i.IIC2_Read_One_Byte) refers to iic2.o(i.IIC2_Read_Byte) for IIC2_Read_Byte
    iic2.o(i.IIC2_Read_One_Byte) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    iic2.o(i.IIC2_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Send_Byte) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    iic2.o(i.IIC2_Start) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Stop) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Wait_Ack) refers to delay.o(i.delay_us) for delay_us
    iic2.o(i.IIC2_Wait_Ack) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    iic2.o(i.IIC2_Write_One_Byte) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    iic2.o(i.IIC2_Write_One_Byte) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    iic2.o(i.IIC2_Write_One_Byte) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    iic2.o(i.IIC2_Write_One_Byte) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    iic3.o(i.IIC3_Ack) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    iic3.o(i.IIC3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    iic3.o(i.IIC3_NAck) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Read_Byte) refers to iic3.o(i.IIC3_NAck) for IIC3_NAck
    iic3.o(i.IIC3_Read_Byte) refers to iic3.o(i.IIC3_Ack) for IIC3_Ack
    iic3.o(i.IIC3_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Start) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Stop) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Wait_Ack) refers to delay.o(i.delay_us) for delay_us
    iic3.o(i.IIC3_Wait_Ack) refers to iic3.o(i.IIC3_Stop) for IIC3_Stop
    main.o(i.init) refers to delay.o(i.delay_init) for delay_init
    main.o(i.init) refers to usart1.o(i.Usart1_Init) for Usart1_Init
    main.o(i.init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.init) refers to grayscale.o(i.Grayscale_Init) for Grayscale_Init
    main.o(i.init) refers to pca9685.o(i.PCA9685_Init) for PCA9685_Init
    main.o(i.init) refers to tb6612.o(i.TB6612_Init) for TB6612_Init
    main.o(i.init) refers to tb6612.o(i.Encoder_Init) for Encoder_Init
    main.o(i.init) refers to tcs34725.o(i.TCS34725_Init) for TCS34725_Init
    main.o(i.init) refers to tb6612.o(i.MOTOR_Init) for MOTOR_Init
    main.o(i.main) refers to main.o(i.init) for init
    main.o(i.main) refers to tb6612.o(i.MotorPID_GO) for MotorPID_GO
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    task_working.o(i.Claw_Action) refers to pca9685.o(i.crazyMe) for crazyMe
    task_working.o(i.Claw_Action) refers to delay.o(i.delay_ms) for delay_ms
    task_working.o(i.Determine_Color) refers to task_working.o(.data) for Now_Position
    task_working.o(i.DuoJi_Init) refers to pca9685.o(i.crazyMe) for crazyMe
    task_working.o(i.Now_Position_Init) refers to task_working.o(.data) for stu
    task_working.o(i.See_Color) refers to tcs34725.o(i.TCS34725_Get_RGBData) for TCS34725_Get_RGBData
    task_working.o(i.See_Color) refers to tcs34725.o(i.TCS34725_GetRGB888) for TCS34725_GetRGB888
    task_working.o(i.See_Color) refers to task_working.o(i.Determine_Color) for Determine_Color
    task_working.o(i.See_Color) refers to tcs34725.o(.data) for R_Dat
    task_working.o(i.See_Color) refers to task_working.o(.data) for Color_Flag
    task_working.o(i.Set_want_Position) refers to grayscale.o(i.ALL_Count) for ALL_Count
    task_working.o(i.Set_want_Position) refers to delay.o(i.delay_ms) for delay_ms
    task_working.o(i.Set_want_Position) refers to gray_go.o(i.Car_TurnAngle) for Car_TurnAngle
    task_working.o(i.Set_want_Position) refers to task_working.o(i.TIME_GO) for TIME_GO
    task_working.o(i.Set_want_Position) refers to buzzer.o(i.BEED) for BEED
    task_working.o(i.Set_want_Position) refers to task_working.o(.data) for Now_Position
    task_working.o(i.Set_want_Position) refers to gray_go.o(i.SearchRun) for SearchRun
    task_working.o(i.Set_want_Position) refers to gray_go.o(i.anti_SearchRun) for anti_SearchRun
    task_working.o(i.TIME_GO) refers to tb6612.o(i.Set_Motor) for Set_Motor
    task_working.o(i.TIME_GO) refers to delay.o(i.delay_ms) for delay_ms
    task_working.o(i.Task1_Working) refers to task_working.o(i.Set_want_Position) for Set_want_Position
    task_working.o(i.Task1_Working) refers to task_working.o(i.Claw_Action) for Claw_Action
    task_working.o(i.Task1_Working) refers to task_working.o(i.TIME_GO) for TIME_GO
    task_working.o(i.Task1_Working) refers to task_working.o(.data) for Now_Location
    task_working.o(i.Task2_Working) refers to task_working.o(i.Set_want_Position) for Set_want_Position
    task_working.o(i.Task2_Working) refers to task_working.o(i.TIME_GO) for TIME_GO
    task_working.o(i.Task2_Working) refers to task_working.o(i.Claw_Action) for Claw_Action
    task_working.o(i.Task2_Working) refers to delay.o(i.delay_ms) for delay_ms
    task_working.o(i.Task2_Working) refers to task_working.o(i.See_Color) for See_Color
    task_working.o(i.Task2_Working) refers to task_working.o(i.Determine_Color) for Determine_Color
    task_working.o(i.Task2_Working) refers to grayscale.o(i.ALL_Count) for ALL_Count
    task_working.o(i.Task2_Working) refers to task_working.o(.data) for Color_Position
    task_working.o(i.Task2_Working) refers to gray_go.o(i.SearchRun) for SearchRun
    task_working.o(i.Task2_Working) refers to gray_go.o(i.anti_SearchRun) for anti_SearchRun
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.accel_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.accel_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.accel_self_test) refers to feqf.o(x$fpl$feqf) for __aeabi_cfcmpeq
    inv_mpu.o(i.accel_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.accel_self_test) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    inv_mpu.o(i.accel_self_test) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.get_accel_prod_shift) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_accel_prod_shift) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.get_st_biases) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.gyro_self_test) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.gyro_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.gyro_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.gyro_self_test) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu.o(i.gyro_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.gyro_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.gyro_self_test) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    inv_mpu.o(i.gyro_self_test) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(i.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(i.mpu_dmp_get_data) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.mpu_dmp_get_data) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.mpu_dmp_get_data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.mpu_dmp_get_data) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(i.mpu_dmp_get_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    inv_mpu.o(i.mpu_dmp_get_data) refers to asin.o(i.asin) for asin
    inv_mpu.o(i.mpu_dmp_get_data) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    inv_mpu.o(i.mpu_dmp_get_data) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    inv_mpu.o(i.mpu_dmp_get_data) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.mpu_dmp_get_data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(i.mpu_dmp_init) refers to iic2.o(i.IIC2_Init) for IIC2_Init
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.run_self_test) for run_self_test
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(.data) for gyro_orientation
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_int_status) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.mpu_get_temperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(i.mpu_get_temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.mpu_get_temperature) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(i.mpu_init) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    inv_mpu.o(i.mpu_init) refers to _printf_dec.o(.text) for _printf_int_dec
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_init) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_init) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.conststring) for .conststring
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo_stream) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reg_dump) refers to _printf_pad.o(.text) for _printf_pre_padding
    inv_mpu.o(i.mpu_reg_dump) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    inv_mpu.o(i.mpu_reg_dump) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    inv_mpu.o(i.mpu_reg_dump) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    inv_mpu.o(i.mpu_reg_dump) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_reg_dump) refers to noretval__2printf.o(.text) for __2printf
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reset_fifo) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_reset_fifo) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_bypass) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_latched) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_lpf) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sensors) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_write_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(i.run_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.run_self_test) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.set_int_enable) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for st
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu_dmp_motion_driver.o(i.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for dmp_memory
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(i.decode_gesture) for decode_gesture
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    mpu6050.o(i.MPU_Get_Accelerometer) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    mpu6050.o(i.MPU_Get_Temperature) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    mpu6050.o(i.MPU_Get_Temperature) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    mpu6050.o(i.MPU_Get_Temperature) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mpu6050.o(i.MPU_Get_Temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    mpu6050.o(i.MPU_Get_Temperature) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    mpu6050.o(i.MPU_Init) refers to iic2.o(i.IIC2_Init) for IIC2_Init
    mpu6050.o(i.MPU_Init) refers to delay.o(i.delay_ms) for delay_ms
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Rate) for MPU_Set_Rate
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Read_Byte) for MPU_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    mpu6050.o(i.MPU_Read_Byte) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    mpu6050.o(i.MPU_Read_Byte) refers to iic2.o(i.IIC2_Read_Byte) for IIC2_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    mpu6050.o(i.MPU_Read_Len) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    mpu6050.o(i.MPU_Read_Len) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    mpu6050.o(i.MPU_Read_Len) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic2.o(i.IIC2_Read_Byte) for IIC2_Read_Byte
    mpu6050.o(i.MPU_Set_Accel_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Gyro_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_LPF) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Set_LPF) for MPU_Set_LPF
    mpu6050.o(i.MPU_Write_Byte) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    mpu6050.o(i.MPU_Write_Byte) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    mpu6050.o(i.MPU_Write_Byte) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    mpu6050.o(i.MPU_Write_Byte) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    mpu6050.o(i.MPU_Write_Len) refers to iic2.o(i.IIC2_Start) for IIC2_Start
    mpu6050.o(i.MPU_Write_Len) refers to iic2.o(i.IIC2_Send_Byte) for IIC2_Send_Byte
    mpu6050.o(i.MPU_Write_Len) refers to iic2.o(i.IIC2_Wait_Ack) for IIC2_Wait_Ack
    mpu6050.o(i.MPU_Write_Len) refers to iic2.o(i.IIC2_Stop) for IIC2_Stop
    sixe_angle.o(i.MPUyaw_Car) refers to sixe_angle.o(i.Scan_MpuVal) for Scan_MpuVal
    sixe_angle.o(i.MPUyaw_Car) refers to tb6612.o(i.Set_Motor) for Set_Motor
    sixe_angle.o(i.MPUyaw_Car) refers to delay.o(i.delay_ms) for delay_ms
    sixe_angle.o(i.MPUyaw_Car) refers to sixe_angle.o(.data) for myaw
    sixe_angle.o(i.Scan_MpuVal) refers to inv_mpu.o(i.mpu_dmp_get_data) for mpu_dmp_get_data
    sixe_angle.o(i.Scan_MpuVal) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    sixe_angle.o(i.Scan_MpuVal) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    sixe_angle.o(i.Scan_MpuVal) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sixe_angle.o(i.Scan_MpuVal) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sixe_angle.o(i.Scan_MpuVal) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    sixe_angle.o(i.Scan_MpuVal) refers to inv_mpu.o(.data) for yaw
    sixe_angle.o(i.Scan_MpuVal) refers to sixe_angle.o(.data) for yaw_count
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart1.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart1.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin.o(i.asin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.__asin$lsc) for __asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.__asin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin_x.o(i.__asin$lsc) refers to _rserrno.o(.text) for __set_errno
    asin_x.o(i.__asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.__asin$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin_x.o(i.__asin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin_x.o(i.__asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin_x.o(i.__asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    floor.o(i.__softfp_floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.floor) for floor
    floor.o(i.floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    floor.o(i.floor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart1.o(i.fputc) for fputc
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart1.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart1.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart1.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing gray_go.o(i.Car_TurnAngle), (84 bytes).
    Removing gray_go.o(i.L_Angle_Count), (876 bytes).
    Removing gray_go.o(i.R_Angle_Count), (876 bytes).
    Removing gray_go.o(i.SearchRun), (1140 bytes).
    Removing gray_go.o(i.anti_SearchRun), (1220 bytes).
    Removing gray_go.o(.data), (4 bytes).
    Removing grayscale.o(i.ALL_Count), (82 bytes).
    Removing grayscale.o(i.RL_Back_Count), (124 bytes).
    Removing grayscale.o(i.RL_Centre_Count), (124 bytes).
    Removing grayscale.o(i.RL_Front_Count), (124 bytes).
    Removing grayscale.o(i.scan_Count_Flag), (76 bytes).
    Removing led.o(i.LED_Init), (56 bytes).
    Removing led.o(i.LED_OFF), (36 bytes).
    Removing led.o(i.LED_ON), (36 bytes).
    Removing tb6612.o(i.Car_Letreat), (14 bytes).
    Removing tb6612.o(i.Car_Retreat), (12 bytes).
    Removing tb6612.o(i.Move_stop), (48 bytes).
    Removing tb6612.o(i.SetPWM), (76 bytes).
    Removing tb6612.o(i.Set_Motor), (272 bytes).
    Removing tcs34725.o(i.Data_Processing), (48 bytes).
    Removing tcs34725.o(i.TCS34725_CheckStatus), (66 bytes).
    Removing tcs34725.o(i.TCS34725_GetRGB565), (418 bytes).
    Removing tcs34725.o(i.TCS34725_GetRGB888), (416 bytes).
    Removing tcs34725.o(i.TCS34725_Get_RGBData), (164 bytes).
    Removing tcs34725.o(i.integrationTime), (88 bytes).
    Removing tcs34725.o(.data), (42 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing pca9685.o(i.calculate_PWM), (100 bytes).
    Removing pca9685.o(i.crazyMe), (144 bytes).
    Removing pca9685.o(i.setPWM), (58 bytes).
    Removing buzzer.o(i.BEED), (44 bytes).
    Removing buzzer.o(i.Buzzer_OFF), (20 bytes).
    Removing buzzer.o(i.Buzzer_ON), (20 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing sys.o(i.Ex_NVIC_Config), (160 bytes).
    Removing sys.o(i.GPIO_AF_Set), (20 bytes).
    Removing sys.o(i.GPIO_Set), (408 bytes).
    Removing sys.o(i.MY_NVIC_Init), (120 bytes).
    Removing sys.o(i.MY_NVIC_PriorityGroupConfig), (40 bytes).
    Removing sys.o(i.MY_NVIC_SetVectorTable), (16 bytes).
    Removing sys.o(i.Stm32_Clock_Init), (152 bytes).
    Removing sys.o(i.Sys_Soft_Reset), (16 bytes).
    Removing sys.o(i.Sys_Standby), (76 bytes).
    Removing uart4.o(i.Uart4_Init), (168 bytes).
    Removing uart4.o(i.mpu6050_send_data), (110 bytes).
    Removing uart4.o(i.uart4_niming_report), (118 bytes).
    Removing uart4.o(i.uart4_report_imu), (192 bytes).
    Removing uart4.o(i.uart4_send_char), (32 bytes).
    Removing uart5.o(i.Uart5_Init), (168 bytes).
    Removing uart5.o(i.Uart5_SendChar), (32 bytes).
    Removing uart5.o(i.Uart5_SendString), (22 bytes).
    Removing usart1.o(i.fputc), (28 bytes).
    Removing usart2.o(i.Get_Usart2Date), (12 bytes).
    Removing usart2.o(i.Get_Usart2Flag), (28 bytes).
    Removing usart2.o(i.USART2_Init), (164 bytes).
    Removing usart2.o(i.Usart2_Send), (32 bytes).
    Removing delay.o(i.delay_s), (24 bytes).
    Removing delay.o(i.delay_xms), (12 bytes).
    Removing usart3.o(i.USART3_SendChar), (32 bytes).
    Removing usart3.o(i.USART3_SendString), (22 bytes).
    Removing usart3.o(i.USART_SendData_Buf), (26 bytes).
    Removing usart3.o(i.Usart3_Init), (168 bytes).
    Removing usart3.o(i.Usart6_Init), (12 bytes).
    Removing usart3.o(i.clearRxBuffer), (20 bytes).
    Removing usart3.o(i.isRxCompleted), (24 bytes).
    Removing tim3.o(i.PWM_SetCompare3_3), (20 bytes).
    Removing tim3.o(i.PWM_SetCompare3_4), (20 bytes).
    Removing tim3.o(i.TIM3_PWM_Init), (168 bytes).
    Removing tim4.o(i.PWM_SetCompare4_3), (20 bytes).
    Removing tim4.o(i.TIM4_PWM_Init), (152 bytes).
    Removing tim8.o(i.PWM_SetCompare8_4), (20 bytes).
    Removing tim8.o(i.TIM8_PWM_Init), (156 bytes).
    Removing iic2.o(i.IIC2_Ack), (80 bytes).
    Removing iic2.o(i.IIC2_Init), (64 bytes).
    Removing iic2.o(i.IIC2_NAck), (80 bytes).
    Removing iic2.o(i.IIC2_Read_Byte), (112 bytes).
    Removing iic2.o(i.IIC2_Read_One_Byte), (64 bytes).
    Removing iic2.o(i.IIC2_Send_Byte), (116 bytes).
    Removing iic2.o(i.IIC2_Start), (76 bytes).
    Removing iic2.o(i.IIC2_Stop), (76 bytes).
    Removing iic2.o(i.IIC2_Wait_Ack), (100 bytes).
    Removing iic2.o(i.IIC2_Write_One_Byte), (48 bytes).
    Removing task_working.o(i.Claw_Action), (740 bytes).
    Removing task_working.o(i.Determine_Color), (136 bytes).
    Removing task_working.o(i.DuoJi_Init), (100 bytes).
    Removing task_working.o(i.Judge_Case), (80 bytes).
    Removing task_working.o(i.Now_Position_Init), (24 bytes).
    Removing task_working.o(i.See_Color), (268 bytes).
    Removing task_working.o(i.Set_want_Position), (660 bytes).
    Removing task_working.o(i.TIME_GO), (122 bytes).
    Removing task_working.o(i.Task1_Working), (476 bytes).
    Removing task_working.o(i.Task2_Working), (712 bytes).
    Removing task_working.o(.data), (169 bytes).
    Removing inv_mpu.o(i.accel_self_test), (168 bytes).
    Removing inv_mpu.o(i.get_accel_prod_shift), (180 bytes).
    Removing inv_mpu.o(i.get_st_biases), (1136 bytes).
    Removing inv_mpu.o(i.gyro_self_test), (272 bytes).
    Removing inv_mpu.o(i.inv_orientation_matrix_to_scalar), (40 bytes).
    Removing inv_mpu.o(i.inv_row_2_scale), (78 bytes).
    Removing inv_mpu.o(i.mget_ms), (2 bytes).
    Removing inv_mpu.o(i.mpu_configure_fifo), (112 bytes).
    Removing inv_mpu.o(i.mpu_dmp_get_data), (548 bytes).
    Removing inv_mpu.o(i.mpu_dmp_init), (156 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_fsr), (76 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_sens), (84 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (10 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_fsr), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_sens), (80 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_lpf), (80 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (28 bytes).
    Removing inv_mpu.o(i.mpu_get_sample_rate), (32 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (160 bytes).
    Removing inv_mpu.o(i.mpu_init), (496 bytes).
    Removing inv_mpu.o(i.mpu_load_firmware), (184 bytes).
    Removing inv_mpu.o(i.mpu_lp_accel_mode), (224 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (516 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (504 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo_stream), (192 bytes).
    Removing inv_mpu.o(i.mpu_read_mem), (128 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (104 bytes).
    Removing inv_mpu.o(i.mpu_reset_fifo), (456 bytes).
    Removing inv_mpu.o(i.mpu_run_self_test), (284 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (384 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_fsr), (132 bytes).
    Removing inv_mpu.o(i.mpu_set_bypass), (332 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_set_dmp_state), (144 bytes).
    Removing inv_mpu.o(i.mpu_set_gyro_fsr), (136 bytes).
    Removing inv_mpu.o(i.mpu_set_int_latched), (108 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(i.mpu_set_lpf), (132 bytes).
    Removing inv_mpu.o(i.mpu_set_sample_rate), (156 bytes).
    Removing inv_mpu.o(i.mpu_set_sensors), (208 bytes).
    Removing inv_mpu.o(i.mpu_write_mem), (128 bytes).
    Removing inv_mpu.o(i.run_self_test), (148 bytes).
    Removing inv_mpu.o(i.set_int_enable), (144 bytes).
    Removing inv_mpu.o(i.setup_compass), (6 bytes).
    Removing inv_mpu.o(.constdata), (80 bytes).
    Removing inv_mpu.o(.conststring), (77 bytes).
    Removing inv_mpu.o(.data), (65 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.decode_gesture), (100 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat), (60 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature), (540 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal), (88 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat), (60 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (66 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware), (24 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo), (460 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias), (304 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate), (112 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias), (304 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (92 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation), (312 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (42 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh), (56 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes), (70 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh), (416 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.constdata), (3062 bytes).
    Removing mpu6050.o(i.MPU_Get_Accelerometer), (80 bytes).
    Removing mpu6050.o(i.MPU_Get_Gyroscope), (80 bytes).
    Removing mpu6050.o(i.MPU_Get_Temperature), (104 bytes).
    Removing mpu6050.o(i.MPU_Init), (150 bytes).
    Removing mpu6050.o(i.MPU_Read_Byte), (58 bytes).
    Removing mpu6050.o(i.MPU_Read_Len), (116 bytes).
    Removing mpu6050.o(i.MPU_Set_Accel_Fsr), (16 bytes).
    Removing mpu6050.o(i.MPU_Set_Gyro_Fsr), (16 bytes).
    Removing mpu6050.o(i.MPU_Set_LPF), (58 bytes).
    Removing mpu6050.o(i.MPU_Set_Rate), (58 bytes).
    Removing mpu6050.o(i.MPU_Write_Byte), (68 bytes).
    Removing mpu6050.o(i.MPU_Write_Len), (96 bytes).
    Removing mpu6050.o(.data), (14 bytes).
    Removing sixe_angle.o(i.MPUyaw_Car), (84 bytes).
    Removing sixe_angle.o(i.Scan_MpuVal), (156 bytes).
    Removing sixe_angle.o(.data), (8 bytes).

644 unused section(s) (total 48971 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Hardware\Buzzer.c                        0x00000000   Number         0  buzzer.o ABSOLUTE
    Hardware\MPU6050\MPU6050.c               0x00000000   Number         0  mpu6050.o ABSOLUTE
    Hardware\MPU6050\inv_mpu.c               0x00000000   Number         0  inv_mpu.o ABSOLUTE
    Hardware\MPU6050\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    Hardware\MPU6050\sixe_angle.c            0x00000000   Number         0  sixe_angle.o ABSOLUTE
    Hardware\PCA9685.c                       0x00000000   Number         0  pca9685.o ABSOLUTE
    Hardware\TB6612.c                        0x00000000   Number         0  tb6612.o ABSOLUTE
    Hardware\TCS34725.c                      0x00000000   Number         0  tcs34725.o ABSOLUTE
    Hardware\gray_go.c                       0x00000000   Number         0  gray_go.o ABSOLUTE
    Hardware\grayscale.c                     0x00000000   Number         0  grayscale.o ABSOLUTE
    Hardware\led.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\oled.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    System\IIC1.c                            0x00000000   Number         0  iic1.o ABSOLUTE
    System\IIC2.c                            0x00000000   Number         0  iic2.o ABSOLUTE
    System\IIC3.c                            0x00000000   Number         0  iic3.o ABSOLUTE
    System\TIM3.c                            0x00000000   Number         0  tim3.o ABSOLUTE
    System\TIM4.c                            0x00000000   Number         0  tim4.o ABSOLUTE
    System\TIM8.c                            0x00000000   Number         0  tim8.o ABSOLUTE
    System\USART2.c                          0x00000000   Number         0  usart2.o ABSOLUTE
    System\Uart4.c                           0x00000000   Number         0  uart4.o ABSOLUTE
    System\Uart5.c                           0x00000000   Number         0  uart5.o ABSOLUTE
    System\Usart1.c                          0x00000000   Number         0  usart1.o ABSOLUTE
    System\Usart3.c                          0x00000000   Number         0  usart3.o ABSOLUTE
    System\\sys.c                            0x00000000   Number         0  sys.o ABSOLUTE
    System\delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    System\sys.c                             0x00000000   Number         0  sys.o ABSOLUTE
    User\Task_Working.c                      0x00000000   Number         0  task_working.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080001ca   Section        0  heapauxi.o(.text)
    .text                                    0x080001d0   Section        2  use_no_semi.o(.text)
    .text                                    0x080001d4   Section        8  libspace.o(.text)
    .text                                    0x080001dc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000226   Section        0  exit.o(.text)
    i.Encoder_Init                           0x08000238   Section        0  tb6612.o(i.Encoder_Init)
    i.GPIO_Init                              0x080002ac   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x080003c4   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ReadInputDataBit                  0x08000454   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000466   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800046a   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800046e   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Get_Encoder_Count                      0x08000478   Section        0  tb6612.o(i.Get_Encoder_Count)
    i.Get_Motor_Position                     0x08000486   Section        0  tb6612.o(i.Get_Motor_Position)
    i.Grayscale_Init                         0x08000494   Section        0  grayscale.o(i.Grayscale_Init)
    i.HDIO_Init                              0x080004a4   Section        0  grayscale.o(i.HDIO_Init)
    i.IIC3_Ack                               0x08000520   Section        0  iic3.o(i.IIC3_Ack)
    i.IIC3_Init                              0x08000570   Section        0  iic3.o(i.IIC3_Init)
    i.IIC3_NAck                              0x080005b0   Section        0  iic3.o(i.IIC3_NAck)
    i.IIC3_Read_Byte                         0x08000600   Section        0  iic3.o(i.IIC3_Read_Byte)
    i.IIC3_Send_Byte                         0x08000670   Section        0  iic3.o(i.IIC3_Send_Byte)
    i.IIC3_Start                             0x080006d8   Section        0  iic3.o(i.IIC3_Start)
    i.IIC3_Stop                              0x08000724   Section        0  iic3.o(i.IIC3_Stop)
    i.IIC3_Wait_Ack                          0x08000770   Section        0  iic3.o(i.IIC3_Wait_Ack)
    i.MOTOR_Init                             0x080007d4   Section        0  tb6612.o(i.MOTOR_Init)
    i.MotorPID_GO                            0x08000814   Section        0  tb6612.o(i.MotorPID_GO)
    i.NVIC_Init                              0x08000924   Section        0  misc.o(i.NVIC_Init)
    i.OLED_Clear                             0x08000994   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080009c0   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08000a0c   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08000a64   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08000a94   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08000abc   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08000b6a   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08000b8c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08000c00   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08000c28   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000c48   Section        0  oled.o(i.OLED_WriteData)
    i.PCA9685_Init                           0x08000c68   Section        0  pca9685.o(i.PCA9685_Init)
    i.PCA9685_read                           0x08000c84   Section        0  pca9685.o(i.PCA9685_read)
    i.PCA9685_write                          0x08000cbe   Section        0  pca9685.o(i.PCA9685_write)
    i.PID_Calculate                          0x08000cec   Section        0  tb6612.o(i.PID_Calculate)
    i.PID_Init                               0x08000da8   Section        0  tb6612.o(i.PID_Init)
    i.RCC_APB1PeriphClockCmd                 0x08000de0   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000e00   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000e20   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Reset_Encoder                          0x08000ef4   Section        0  tb6612.o(i.Reset_Encoder)
    i.SDA_Pin_IN                             0x08000f04   Section        0  iic1.o(i.SDA_Pin_IN)
    i.SDA_Pin_Output                         0x08000f30   Section        0  iic1.o(i.SDA_Pin_Output)
    i.SetSysClock                            0x08000f4c   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08000f4d   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000f54   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000f55   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_Motor_PWM                          0x08001034   Section        0  tb6612.o(i.Set_Motor_PWM)
    i.SystemInit                             0x08001174   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TB6612_Init                            0x080011d4   Section        0  tb6612.o(i.TB6612_Init)
    i.TCS34725_GPIO_Init                     0x08001310   Section        0  iic1.o(i.TCS34725_GPIO_Init)
    i.TCS34725_IIC_ACK                       0x08001348   Section        0  iic1.o(i.TCS34725_IIC_ACK)
    i.TCS34725_IIC_Delay                     0x08001378   Section        0  iic1.o(i.TCS34725_IIC_Delay)
    i.TCS34725_IIC_Get_ack                   0x08001384   Section        0  iic1.o(i.TCS34725_IIC_Get_ack)
    i.TCS34725_IIC_Init                      0x080013dc   Section        0  iic1.o(i.TCS34725_IIC_Init)
    i.TCS34725_IIC_NACK                      0x08001400   Section        0  iic1.o(i.TCS34725_IIC_NACK)
    i.TCS34725_IIC_read_byte                 0x0800142c   Section        0  iic1.o(i.TCS34725_IIC_read_byte)
    i.TCS34725_IIC_start                     0x08001494   Section        0  iic1.o(i.TCS34725_IIC_start)
    i.TCS34725_IIC_stop                      0x080014cc   Section        0  iic1.o(i.TCS34725_IIC_stop)
    i.TCS34725_IIC_write_byte                0x08001504   Section        0  iic1.o(i.TCS34725_IIC_write_byte)
    i.TCS34725_Init                          0x08001560   Section        0  tcs34725.o(i.TCS34725_Init)
    i.TCS34725_ReadWord                      0x080015a4   Section        0  tcs34725.o(i.TCS34725_ReadWord)
    i.TCS34725_WriteByte                     0x080015fc   Section        0  tcs34725.o(i.TCS34725_WriteByte)
    i.TIM_ARRPreloadConfig                   0x0800162a   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_Cmd                                0x08001642   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x0800165a   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_EncoderInterfaceConfig             0x08001678   Section        0  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    i.TIM_GetCounter                         0x080016ba   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_OC3Init                            0x080016c0   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08001760   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x08001774   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x080017f0   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_SetCompare3                        0x0800180a   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_SetCompare4                        0x0800180e   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_SetCounter                         0x08001814   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_TimeBaseInit                       0x08001818   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Turns_To_Pulses                        0x080018bc   Section        0  tb6612.o(i.Turns_To_Pulses)
    i.USART1_IRQHandler                      0x080018d4   Section        0  usart1.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x0800195c   Section        0  usart2.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08001994   Section        0  usart3.o(i.USART3_IRQHandler)
    i.USART_ClearITPendingBit                0x080019f0   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001a0e   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001a26   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001a7a   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001ac4   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001b9c   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.Usart1_Init                            0x08001ba8   Section        0  usart1.o(i.Usart1_Init)
    i._sys_exit                              0x08001c48   Section        0  usart1.o(i._sys_exit)
    i.delay_init                             0x08001c4c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001c7c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08001c94   Section        0  delay.o(i.delay_us)
    i.eight_gray1                            0x08001cd0   Section        0  grayscale.o(i.eight_gray1)
    i.eight_gray2                            0x08001d24   Section        0  grayscale.o(i.eight_gray2)
    i.floor                                  0x08001d88   Section        0  floor.o(i.floor)
    i.init                                   0x08001e64   Section        0  main.o(i.init)
    i.main                                   0x08001e98   Section        0  main.o(i.main)
    i.motor_mode                             0x08001ec0   Section        0  tb6612.o(i.motor_mode)
    i.setPWMFreq                             0x08001f74   Section        0  pca9685.o(i.setPWMFreq)
    x$fpl$dadd                               0x0800200c   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800201d   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x0800215c   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08002174   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800217b   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08002424   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800247e   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dleqf                              0x080024ac   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08002524   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002678   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002714   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08002720   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800278c   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x080027a4   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080027b5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fadd                               0x08002978   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08002987   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x08002a3c   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$ffix                               0x08002a54   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x08002a8c   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fleqf                              0x08002abc   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08002b24   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08002c26   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08002cb2   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08002cbc   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x08002d20   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08002d2f   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    .constdata                               0x08002e0a   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x08002e0a   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        1  tb6612.o(.data)
    pid_initialized                          0x20000014   Data           1  tb6612.o(.data)
    .data                                    0x20000018   Section        6  usart1.o(.data)
    .data                                    0x2000001e   Section        2  usart2.o(.data)
    .data                                    0x20000020   Section        4  delay.o(.data)
    fac_us                                   0x20000020   Data           1  delay.o(.data)
    fac_ms                                   0x20000022   Data           2  delay.o(.data)
    .data                                    0x20000024   Section        3  usart3.o(.data)
    USART3_RxCounter                         0x20000024   Data           2  usart3.o(.data)
    USART3_RxComplete                        0x20000026   Data           1  usart3.o(.data)
    .bss                                     0x20000028   Section       48  tb6612.o(.bss)
    motor_pid                                0x20000028   Data          48  tb6612.o(.bss)
    .bss                                     0x20000058   Section      200  usart1.o(.bss)
    .bss                                     0x20000120   Section       64  usart3.o(.bss)
    USART3_RxBuffer                          0x20000120   Data          64  usart3.o(.bss)
    .bss                                     0x20000160   Section       96  libspace.o(.bss)
    HEAP                                     0x200001c0   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200001c0   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200003c0   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200003c0   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x200007c0   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    NMI_Handler                              0x08000191   Thumb Code     2  startup_stm32f10x_md.o(.text)
    HardFault_Handler                        0x08000193   Thumb Code     2  startup_stm32f10x_md.o(.text)
    MemManage_Handler                        0x08000195   Thumb Code     2  startup_stm32f10x_md.o(.text)
    BusFault_Handler                         0x08000197   Thumb Code     2  startup_stm32f10x_md.o(.text)
    UsageFault_Handler                       0x08000199   Thumb Code     2  startup_stm32f10x_md.o(.text)
    SVC_Handler                              0x0800019b   Thumb Code     2  startup_stm32f10x_md.o(.text)
    DebugMon_Handler                         0x0800019d   Thumb Code     2  startup_stm32f10x_md.o(.text)
    PendSV_Handler                           0x0800019f   Thumb Code     2  startup_stm32f10x_md.o(.text)
    SysTick_Handler                          0x080001a1   Thumb Code     2  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_no_semihosting                     0x080001c9   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x080001cb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080001cd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080001cf   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080001d1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080001d1   Thumb Code     2  use_no_semi.o(.text)
    __user_libspace                          0x080001d5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080001d5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080001d5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080001dd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000227   Thumb Code    18  exit.o(.text)
    Encoder_Init                             0x08000239   Thumb Code   110  tb6612.o(i.Encoder_Init)
    GPIO_Init                                0x080002ad   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x080003c5   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ReadInputDataBit                    0x08000455   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000467   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800046b   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800046f   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Get_Encoder_Count                        0x08000479   Thumb Code    14  tb6612.o(i.Get_Encoder_Count)
    Get_Motor_Position                       0x08000487   Thumb Code    14  tb6612.o(i.Get_Motor_Position)
    Grayscale_Init                           0x08000495   Thumb Code    16  grayscale.o(i.Grayscale_Init)
    HDIO_Init                                0x080004a5   Thumb Code   110  grayscale.o(i.HDIO_Init)
    IIC3_Ack                                 0x08000521   Thumb Code    66  iic3.o(i.IIC3_Ack)
    IIC3_Init                                0x08000571   Thumb Code    52  iic3.o(i.IIC3_Init)
    IIC3_NAck                                0x080005b1   Thumb Code    66  iic3.o(i.IIC3_NAck)
    IIC3_Read_Byte                           0x08000601   Thumb Code    98  iic3.o(i.IIC3_Read_Byte)
    IIC3_Send_Byte                           0x08000671   Thumb Code    90  iic3.o(i.IIC3_Send_Byte)
    IIC3_Start                               0x080006d9   Thumb Code    64  iic3.o(i.IIC3_Start)
    IIC3_Stop                                0x08000725   Thumb Code    62  iic3.o(i.IIC3_Stop)
    IIC3_Wait_Ack                            0x08000771   Thumb Code    86  iic3.o(i.IIC3_Wait_Ack)
    MOTOR_Init                               0x080007d5   Thumb Code    52  tb6612.o(i.MOTOR_Init)
    MotorPID_GO                              0x08000815   Thumb Code   252  tb6612.o(i.MotorPID_GO)
    NVIC_Init                                0x08000925   Thumb Code   100  misc.o(i.NVIC_Init)
    OLED_Clear                               0x08000995   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080009c1   Thumb Code    72  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08000a0d   Thumb Code    82  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08000a65   Thumb Code    44  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08000a95   Thumb Code    34  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08000abd   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08000b6b   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08000b8d   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08000c01   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08000c29   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000c49   Thumb Code    32  oled.o(i.OLED_WriteData)
    PCA9685_Init                             0x08000c69   Thumb Code    28  pca9685.o(i.PCA9685_Init)
    PCA9685_read                             0x08000c85   Thumb Code    58  pca9685.o(i.PCA9685_read)
    PCA9685_write                            0x08000cbf   Thumb Code    46  pca9685.o(i.PCA9685_write)
    PID_Calculate                            0x08000ced   Thumb Code   186  tb6612.o(i.PID_Calculate)
    PID_Init                                 0x08000da9   Thumb Code    42  tb6612.o(i.PID_Init)
    RCC_APB1PeriphClockCmd                   0x08000de1   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08000e01   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000e21   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Reset_Encoder                            0x08000ef5   Thumb Code    14  tb6612.o(i.Reset_Encoder)
    SDA_Pin_IN                               0x08000f05   Thumb Code    40  iic1.o(i.SDA_Pin_IN)
    SDA_Pin_Output                           0x08000f31   Thumb Code    24  iic1.o(i.SDA_Pin_Output)
    Set_Motor_PWM                            0x08001035   Thumb Code   296  tb6612.o(i.Set_Motor_PWM)
    SystemInit                               0x08001175   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TB6612_Init                              0x080011d5   Thumb Code   292  tb6612.o(i.TB6612_Init)
    TCS34725_GPIO_Init                       0x08001311   Thumb Code    50  iic1.o(i.TCS34725_GPIO_Init)
    TCS34725_IIC_ACK                         0x08001349   Thumb Code    42  iic1.o(i.TCS34725_IIC_ACK)
    TCS34725_IIC_Delay                       0x08001379   Thumb Code    10  iic1.o(i.TCS34725_IIC_Delay)
    TCS34725_IIC_Get_ack                     0x08001385   Thumb Code    84  iic1.o(i.TCS34725_IIC_Get_ack)
    TCS34725_IIC_Init                        0x080013dd   Thumb Code    32  iic1.o(i.TCS34725_IIC_Init)
    TCS34725_IIC_NACK                        0x08001401   Thumb Code    38  iic1.o(i.TCS34725_IIC_NACK)
    TCS34725_IIC_read_byte                   0x0800142d   Thumb Code   100  iic1.o(i.TCS34725_IIC_read_byte)
    TCS34725_IIC_start                       0x08001495   Thumb Code    52  iic1.o(i.TCS34725_IIC_start)
    TCS34725_IIC_stop                        0x080014cd   Thumb Code    52  iic1.o(i.TCS34725_IIC_stop)
    TCS34725_IIC_write_byte                  0x08001505   Thumb Code    88  iic1.o(i.TCS34725_IIC_write_byte)
    TCS34725_Init                            0x08001561   Thumb Code    68  tcs34725.o(i.TCS34725_Init)
    TCS34725_ReadWord                        0x080015a5   Thumb Code    88  tcs34725.o(i.TCS34725_ReadWord)
    TCS34725_WriteByte                       0x080015fd   Thumb Code    46  tcs34725.o(i.TCS34725_WriteByte)
    TIM_ARRPreloadConfig                     0x0800162b   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_Cmd                                  0x08001643   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x0800165b   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_EncoderInterfaceConfig               0x08001679   Thumb Code    66  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    TIM_GetCounter                           0x080016bb   Thumb Code     6  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_OC3Init                              0x080016c1   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001761   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001775   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x080017f1   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_SetCompare3                          0x0800180b   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_SetCompare4                          0x0800180f   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_SetCounter                           0x08001815   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_TimeBaseInit                         0x08001819   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Turns_To_Pulses                          0x080018bd   Thumb Code    20  tb6612.o(i.Turns_To_Pulses)
    USART1_IRQHandler                        0x080018d5   Thumb Code   122  usart1.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x0800195d   Thumb Code    44  usart2.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08001995   Thumb Code    76  usart3.o(i.USART3_IRQHandler)
    USART_ClearITPendingBit                  0x080019f1   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001a0f   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001a27   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001a7b   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001ac5   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001b9d   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    Usart1_Init                              0x08001ba9   Thumb Code   152  usart1.o(i.Usart1_Init)
    _sys_exit                                0x08001c49   Thumb Code     4  usart1.o(i._sys_exit)
    delay_init                               0x08001c4d   Thumb Code    40  delay.o(i.delay_init)
    delay_ms                                 0x08001c7d   Thumb Code    24  delay.o(i.delay_ms)
    delay_us                                 0x08001c95   Thumb Code    56  delay.o(i.delay_us)
    eight_gray1                              0x08001cd1   Thumb Code    74  grayscale.o(i.eight_gray1)
    eight_gray2                              0x08001d25   Thumb Code    88  grayscale.o(i.eight_gray2)
    floor                                    0x08001d89   Thumb Code   204  floor.o(i.floor)
    init                                     0x08001e65   Thumb Code    52  main.o(i.init)
    main                                     0x08001e99   Thumb Code    28  main.o(i.main)
    motor_mode                               0x08001ec1   Thumb Code   152  tb6612.o(i.motor_mode)
    setPWMFreq                               0x08001f75   Thumb Code   134  pca9685.o(i.setPWMFreq)
    __aeabi_dadd                             0x0800200d   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800200d   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x0800215d   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08002175   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002175   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08002425   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08002425   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800247f   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800247f   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_cdcmple                          0x080024ad   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x080024ad   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800250f   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08002525   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002525   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002679   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002715   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08002721   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08002721   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800278d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800278d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x080027a5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080027a5   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_fadd                             0x08002979   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08002979   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x08002a3d   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_f2iz                             0x08002a55   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08002a55   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x08002a8d   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08002a8d   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_cfcmple                          0x08002abd   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08002abd   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08002b0f   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08002b25   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08002b25   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08002c27   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08002cb3   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08002cbd   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08002cbd   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x08002d21   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08002d21   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    OLED_F8x16                               0x08002e0a   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x08002e0a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080033fc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800341c   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000018   Data           4  usart1.o(.data)
    USART_RX_STA                             0x2000001c   Data           2  usart1.o(.data)
    ReciveDatE                               0x2000001e   Data           1  usart2.o(.data)
    ReciveFlag                               0x2000001f   Data           1  usart2.o(.data)
    USART_RX_BUF                             0x20000058   Data         200  usart1.o(.bss)
    __libspace_start                         0x20000160   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001c0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003444, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000341c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          132    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         5141  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         5480    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         5482    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         5484    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         5347    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5359    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5361    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5364    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5366    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5368    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5371    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5373    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5375    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5377    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5379    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5381    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5383    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5385    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5387    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5389    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5391    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5395    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5397    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5399    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         5401    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         5402    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         5432    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5446    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5448    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5451    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5454    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5456    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         5459    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         5460    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         5229    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         5294    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         5306    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         5296    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         5297    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         5299    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         5300    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         5352    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         5406    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         5407    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         5408    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000040   Code   RO          133    .text               startup_stm32f10x_md.o
    0x080001c8   0x080001c8   0x00000002   Code   RO         5077    .text               c_w.l(use_no_semi_2.o)
    0x080001ca   0x080001ca   0x00000006   Code   RO         5139    .text               c_w.l(heapauxi.o)
    0x080001d0   0x080001d0   0x00000002   Code   RO         5227    .text               c_w.l(use_no_semi.o)
    0x080001d2   0x080001d2   0x00000002   PAD
    0x080001d4   0x080001d4   0x00000008   Code   RO         5331    .text               c_w.l(libspace.o)
    0x080001dc   0x080001dc   0x0000004a   Code   RO         5334    .text               c_w.l(sys_stackheap_outer.o)
    0x08000226   0x08000226   0x00000012   Code   RO         5340    .text               c_w.l(exit.o)
    0x08000238   0x08000238   0x00000074   Code   RO         3351    i.Encoder_Init      tb6612.o
    0x080002ac   0x080002ac   0x00000116   Code   RO         1350    i.GPIO_Init         stm32f10x_gpio.o
    0x080003c2   0x080003c2   0x00000002   PAD
    0x080003c4   0x080003c4   0x00000090   Code   RO         1352    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x08000454   0x08000454   0x00000012   Code   RO         1354    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000466   0x08000466   0x00000004   Code   RO         1357    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x0800046a   0x0800046a   0x00000004   Code   RO         1358    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800046e   0x0800046e   0x0000000a   Code   RO         1361    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000478   0x08000478   0x0000000e   Code   RO         3352    i.Get_Encoder_Count  tb6612.o
    0x08000486   0x08000486   0x0000000e   Code   RO         3353    i.Get_Motor_Position  tb6612.o
    0x08000494   0x08000494   0x00000010   Code   RO         3263    i.Grayscale_Init    grayscale.o
    0x080004a4   0x080004a4   0x0000007c   Code   RO         3264    i.HDIO_Init         grayscale.o
    0x08000520   0x08000520   0x00000050   Code   RO         4261    i.IIC3_Ack          iic3.o
    0x08000570   0x08000570   0x00000040   Code   RO         4262    i.IIC3_Init         iic3.o
    0x080005b0   0x080005b0   0x00000050   Code   RO         4263    i.IIC3_NAck         iic3.o
    0x08000600   0x08000600   0x00000070   Code   RO         4264    i.IIC3_Read_Byte    iic3.o
    0x08000670   0x08000670   0x00000068   Code   RO         4265    i.IIC3_Send_Byte    iic3.o
    0x080006d8   0x080006d8   0x0000004c   Code   RO         4266    i.IIC3_Start        iic3.o
    0x08000724   0x08000724   0x0000004c   Code   RO         4267    i.IIC3_Stop         iic3.o
    0x08000770   0x08000770   0x00000064   Code   RO         4268    i.IIC3_Wait_Ack     iic3.o
    0x080007d4   0x080007d4   0x00000040   Code   RO         3354    i.MOTOR_Init        tb6612.o
    0x08000814   0x08000814   0x00000110   Code   RO         3355    i.MotorPID_GO       tb6612.o
    0x08000924   0x08000924   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08000994   0x08000994   0x0000002a   Code   RO         3536    i.OLED_Clear        oled.o
    0x080009be   0x080009be   0x00000002   PAD
    0x080009c0   0x080009c0   0x0000004c   Code   RO         3537    i.OLED_I2C_Init     oled.o
    0x08000a0c   0x08000a0c   0x00000058   Code   RO         3538    i.OLED_I2C_SendByte  oled.o
    0x08000a64   0x08000a64   0x00000030   Code   RO         3539    i.OLED_I2C_Start    oled.o
    0x08000a94   0x08000a94   0x00000028   Code   RO         3540    i.OLED_I2C_Stop     oled.o
    0x08000abc   0x08000abc   0x000000ae   Code   RO         3541    i.OLED_Init         oled.o
    0x08000b6a   0x08000b6a   0x00000022   Code   RO         3543    i.OLED_SetCursor    oled.o
    0x08000b8c   0x08000b8c   0x00000074   Code   RO         3545    i.OLED_ShowChar     oled.o
    0x08000c00   0x08000c00   0x00000028   Code   RO         3549    i.OLED_ShowString   oled.o
    0x08000c28   0x08000c28   0x00000020   Code   RO         3550    i.OLED_WriteCommand  oled.o
    0x08000c48   0x08000c48   0x00000020   Code   RO         3551    i.OLED_WriteData    oled.o
    0x08000c68   0x08000c68   0x0000001c   Code   RO         3644    i.PCA9685_Init      pca9685.o
    0x08000c84   0x08000c84   0x0000003a   Code   RO         3645    i.PCA9685_read      pca9685.o
    0x08000cbe   0x08000cbe   0x0000002e   Code   RO         3646    i.PCA9685_write     pca9685.o
    0x08000cec   0x08000cec   0x000000ba   Code   RO         3357    i.PID_Calculate     tb6612.o
    0x08000da6   0x08000da6   0x00000002   PAD
    0x08000da8   0x08000da8   0x00000038   Code   RO         3358    i.PID_Init          tb6612.o
    0x08000de0   0x08000de0   0x00000020   Code   RO         1778    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08000e00   0x08000e00   0x00000020   Code   RO         1780    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08000e20   0x08000e20   0x000000d4   Code   RO         1788    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000ef4   0x08000ef4   0x0000000e   Code   RO         3359    i.Reset_Encoder     tb6612.o
    0x08000f02   0x08000f02   0x00000002   PAD
    0x08000f04   0x08000f04   0x0000002c   Code   RO         4111    i.SDA_Pin_IN        iic1.o
    0x08000f30   0x08000f30   0x0000001c   Code   RO         4112    i.SDA_Pin_Output    iic1.o
    0x08000f4c   0x08000f4c   0x00000008   Code   RO            1    i.SetSysClock       system_stm32f10x.o
    0x08000f54   0x08000f54   0x000000e0   Code   RO            2    i.SetSysClockTo72   system_stm32f10x.o
    0x08001034   0x08001034   0x00000140   Code   RO         3362    i.Set_Motor_PWM     tb6612.o
    0x08001174   0x08001174   0x00000060   Code   RO            4    i.SystemInit        system_stm32f10x.o
    0x080011d4   0x080011d4   0x0000013c   Code   RO         3363    i.TB6612_Init       tb6612.o
    0x08001310   0x08001310   0x00000038   Code   RO         4113    i.TCS34725_GPIO_Init  iic1.o
    0x08001348   0x08001348   0x00000030   Code   RO         4114    i.TCS34725_IIC_ACK  iic1.o
    0x08001378   0x08001378   0x0000000a   Code   RO         4115    i.TCS34725_IIC_Delay  iic1.o
    0x08001382   0x08001382   0x00000002   PAD
    0x08001384   0x08001384   0x00000058   Code   RO         4116    i.TCS34725_IIC_Get_ack  iic1.o
    0x080013dc   0x080013dc   0x00000024   Code   RO         4117    i.TCS34725_IIC_Init  iic1.o
    0x08001400   0x08001400   0x0000002c   Code   RO         4118    i.TCS34725_IIC_NACK  iic1.o
    0x0800142c   0x0800142c   0x00000068   Code   RO         4119    i.TCS34725_IIC_read_byte  iic1.o
    0x08001494   0x08001494   0x00000038   Code   RO         4120    i.TCS34725_IIC_start  iic1.o
    0x080014cc   0x080014cc   0x00000038   Code   RO         4121    i.TCS34725_IIC_stop  iic1.o
    0x08001504   0x08001504   0x0000005c   Code   RO         4122    i.TCS34725_IIC_write_byte  iic1.o
    0x08001560   0x08001560   0x00000044   Code   RO         3472    i.TCS34725_Init     tcs34725.o
    0x080015a4   0x080015a4   0x00000058   Code   RO         3473    i.TCS34725_ReadWord  tcs34725.o
    0x080015fc   0x080015fc   0x0000002e   Code   RO         3474    i.TCS34725_WriteByte  tcs34725.o
    0x0800162a   0x0800162a   0x00000018   Code   RO         2412    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08001642   0x08001642   0x00000018   Code   RO         2424    i.TIM_Cmd           stm32f10x_tim.o
    0x0800165a   0x0800165a   0x0000001e   Code   RO         2426    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08001678   0x08001678   0x00000042   Code   RO         2433    i.TIM_EncoderInterfaceConfig  stm32f10x_tim.o
    0x080016ba   0x080016ba   0x00000006   Code   RO         2443    i.TIM_GetCounter    stm32f10x_tim.o
    0x080016c0   0x080016c0   0x000000a0   Code   RO         2463    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001760   0x08001760   0x00000012   Code   RO         2466    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001772   0x08001772   0x00000002   PAD
    0x08001774   0x08001774   0x0000007c   Code   RO         2468    i.TIM_OC4Init       stm32f10x_tim.o
    0x080017f0   0x080017f0   0x0000001a   Code   RO         2470    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x0800180a   0x0800180a   0x00000004   Code   RO         2487    i.TIM_SetCompare3   stm32f10x_tim.o
    0x0800180e   0x0800180e   0x00000006   Code   RO         2488    i.TIM_SetCompare4   stm32f10x_tim.o
    0x08001814   0x08001814   0x00000004   Code   RO         2489    i.TIM_SetCounter    stm32f10x_tim.o
    0x08001818   0x08001818   0x000000a4   Code   RO         2495    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x080018bc   0x080018bc   0x00000018   Code   RO         3364    i.Turns_To_Pulses   tb6612.o
    0x080018d4   0x080018d4   0x00000088   Code   RO         3867    i.USART1_IRQHandler  usart1.o
    0x0800195c   0x0800195c   0x00000038   Code   RO         3910    i.USART2_IRQHandler  usart2.o
    0x08001994   0x08001994   0x0000005c   Code   RO         3991    i.USART3_IRQHandler  usart3.o
    0x080019f0   0x080019f0   0x0000001e   Code   RO         2960    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001a0e   0x08001a0e   0x00000018   Code   RO         2963    i.USART_Cmd         stm32f10x_usart.o
    0x08001a26   0x08001a26   0x00000054   Code   RO         2967    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001a7a   0x08001a7a   0x0000004a   Code   RO         2969    i.USART_ITConfig    stm32f10x_usart.o
    0x08001ac4   0x08001ac4   0x000000d8   Code   RO         2970    i.USART_Init        stm32f10x_usart.o
    0x08001b9c   0x08001b9c   0x0000000a   Code   RO         2977    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001ba6   0x08001ba6   0x00000002   PAD
    0x08001ba8   0x08001ba8   0x000000a0   Code   RO         3868    i.Usart1_Init       usart1.o
    0x08001c48   0x08001c48   0x00000004   Code   RO         3869    i._sys_exit         usart1.o
    0x08001c4c   0x08001c4c   0x00000030   Code   RO         3950    i.delay_init        delay.o
    0x08001c7c   0x08001c7c   0x00000018   Code   RO         3951    i.delay_ms          delay.o
    0x08001c94   0x08001c94   0x0000003c   Code   RO         3953    i.delay_us          delay.o
    0x08001cd0   0x08001cd0   0x00000054   Code   RO         3268    i.eight_gray1       grayscale.o
    0x08001d24   0x08001d24   0x00000064   Code   RO         3269    i.eight_gray2       grayscale.o
    0x08001d88   0x08001d88   0x000000dc   Code   RO         5224    i.floor             m_ws.l(floor.o)
    0x08001e64   0x08001e64   0x00000034   Code   RO         4318    i.init              main.o
    0x08001e98   0x08001e98   0x00000028   Code   RO         4319    i.main              main.o
    0x08001ec0   0x08001ec0   0x000000b4   Code   RO         3365    i.motor_mode        tb6612.o
    0x08001f74   0x08001f74   0x00000098   Code   RO         3650    i.setPWMFreq        pca9685.o
    0x0800200c   0x0800200c   0x00000150   Code   RO         5145    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x0800215c   0x0800215c   0x00000018   Code   RO         5348    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x08002174   0x08002174   0x000002b0   Code   RO         5152    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002424   0x08002424   0x0000005a   Code   RO         5159    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x0800247e   0x0800247e   0x0000002e   Code   RO         5164    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x080024ac   0x080024ac   0x00000078   Code   RO         5327    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08002524   0x08002524   0x00000154   Code   RO         5169    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002678   0x08002678   0x0000009c   Code   RO         5241    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08002714   0x08002714   0x0000000c   Code   RO         5243    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08002720   0x08002720   0x0000006c   Code   RO         5245    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x0800278c   0x0800278c   0x00000016   Code   RO         5146    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x080027a2   0x080027a2   0x00000002   PAD
    0x080027a4   0x080027a4   0x000001d4   Code   RO         5147    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08002978   0x08002978   0x000000c4   Code   RO         5173    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08002a3c   0x08002a3c   0x00000018   Code   RO         5247    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08002a54   0x08002a54   0x00000036   Code   RO         5185    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08002a8a   0x08002a8a   0x00000002   PAD
    0x08002a8c   0x08002a8c   0x00000030   Code   RO         5194    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08002abc   0x08002abc   0x00000068   Code   RO         5199    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08002b24   0x08002b24   0x00000102   Code   RO         5201    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08002c26   0x08002c26   0x0000008c   Code   RO         5249    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08002cb2   0x08002cb2   0x0000000a   Code   RO         5251    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002cbc   0x08002cbc   0x00000062   Code   RO         5203    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08002d1e   0x08002d1e   0x00000002   PAD
    0x08002d20   0x08002d20   0x000000ea   Code   RO         5175    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08002e0a   0x08002e0a   0x00000000   Code   RO         5255    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08002e0a   0x08002e0a   0x000005f0   Data   RO         3552    .constdata          oled.o
    0x080033fa   0x080033fa   0x00000002   PAD
    0x080033fc   0x080033fc   0x00000020   Data   RO         5478    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800341c, Size: 0x000007c0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800341c   0x00000014   Data   RW         1808    .data               stm32f10x_rcc.o
    0x20000014   0x08003430   0x00000001   Data   RW         3367    .data               tb6612.o
    0x20000015   0x08003431   0x00000003   PAD
    0x20000018   0x08003434   0x00000006   Data   RW         3872    .data               usart1.o
    0x2000001e   0x0800343a   0x00000002   Data   RW         3913    .data               usart2.o
    0x20000020   0x0800343c   0x00000004   Data   RW         3955    .data               delay.o
    0x20000024   0x08003440   0x00000003   Data   RW         4000    .data               usart3.o
    0x20000027   0x08003443   0x00000001   PAD
    0x20000028        -       0x00000030   Zero   RW         3366    .bss                tb6612.o
    0x20000058        -       0x000000c8   Zero   RW         3871    .bss                usart1.o
    0x20000120        -       0x00000040   Zero   RW         3999    .bss                usart3.o
    0x20000160        -       0x00000060   Zero   RW         5332    .bss                c_w.l(libspace.o)
    0x200001c0        -       0x00000200   Zero   RW          131    HEAP                startup_stm32f10x_md.o
    0x200003c0        -       0x00000400   Zero   RW          130    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       132         12          0          4          0       1587   delay.o
         0          0          0          0          0        816   gray_go.o
       324         36          0          0          0       1949   grayscale.o
       662         50          0          0          0       5626   iic1.o
       692        108          0          0          0       3970   iic3.o
        92         12          0          0          0        818   main.o
       112         12          0          0          0     205628   misc.o
       722         26       1520          0          0       6219   oled.o
       284         18          0          0          0       2175   pca9685.o
        64         26        236          0       1536        784   startup_stm32f10x_md.o
         0          0          0          0          0       1648   stm32f10x_adc.o
       458          6          0          0          0      13062   stm32f10x_gpio.o
       276         32          0         20          0      12922   stm32f10x_rcc.o
       656         62          0          0          0      28058   stm32f10x_tim.o
       438          6          0          0          0      11304   stm32f10x_usart.o
       328         28          0          0          0      49649   system_stm32f10x.o
      1576        132          0          1         48       7563   tb6612.o
       202          0          0          0          0       4365   tcs34725.o
       300         22          0          6        200       3434   usart1.o
        56         12          0          2          0        696   usart2.o
        92         16          0          3         64       1060   usart3.o

    ----------------------------------------------------------------------
      7480        <USER>       <GROUP>         40       1848     363365   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          2          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        348   daddsub_clz.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
        90          4          0          0          0         92   dfixu.o
        46          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
        54          4          0          0          0         84   ffix.o
        48          0          0          0          0         68   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         0          0          0          0          0          0   usenofp.o
       220         16          0          0          0         84   floor.o

    ----------------------------------------------------------------------
      4070        <USER>          <GROUP>          0         96       2664   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       262         12          0          0         96        584   c_w.l
      3576        204          0          0          0       1996   fz_ws.l
       220         16          0          0          0         84   m_ws.l

    ----------------------------------------------------------------------
      4070        <USER>          <GROUP>          0         96       2664   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11550        848       1790         40       1944     359889   Grand Totals
     11550        848       1790         40       1944     359889   ELF Image Totals
     11550        848       1790         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13340 (  13.03kB)
    Total RW  Size (RW Data + ZI Data)              1984 (   1.94kB)
    Total ROM Size (Code + RO Data + RW Data)      13380 (  13.07kB)

==============================================================================

